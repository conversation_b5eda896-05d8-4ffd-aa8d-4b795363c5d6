#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements. See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License. You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

name: Bug Report
description: If you would like to report an issue to Seata, please use this template.

body:
- type: markdown
  attributes:
    value: Please do not use this issue template to report security vulnerabilities but refer to our [security policy](https://github.com/seata/seata/security/policy).

- type: checkboxes
  attributes:
    label: Check Ahead
    options:
    - label: >
        I have searched the [issues](https://github.com/seata/seata/issues) of this repository and believe that this is not a duplicate.
      required: true

- type: textarea
  attributes:
    label: Ⅰ. Issue Description
  validations:
    required: false

- type: textarea
  attributes:
    label: Ⅱ. Describe what happened
    placeholder: >
      If there is an exception, please attach the exception trace:
      
      ```
      Just paste your stack trace here!
      ```
  validations:
    required: false

- type: textarea
  attributes:
    label: Ⅲ. Describe what you expected to happen
  validations:
    required: false

- type: textarea
  attributes:
    label: Ⅳ. How to reproduce it (as minimally and precisely as possible)
    placeholder: >
      1. xxx
      2. xxx
      3. xxx

      Minimal yet complete reproducer code (or URL to code):
  validations:
    required: false

- type: textarea
  attributes:
    label: Ⅴ. Anything else we need to know?
  validations:
    required: false

- type: textarea
  attributes:
    label: Ⅵ. Environment
    placeholder: >
      - JDK version(e.g. `java -version`):
      - Seata client/server version:
      - Database version:
      - OS(e.g. `uname -a`):
      - Others:      
  validations:
    required: false
