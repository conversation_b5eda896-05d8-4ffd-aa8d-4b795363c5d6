#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
name: "build"

on:
  push:
    branches: [ 2.x, develop, master ]
  pull_request:
    branches: [ 2.x, develop, master ]
    types: [opened, reopened, synchronize]
    paths-ignore:
      - '**.md'

jobs:
  # job 1: Test based on java 8, 17 and 21. Do not checkstyle.
  build:
    name: "build"
    services:
      redis:
        image: redis:7.2
        ports:
          - 6379:6379
        options: --health-cmd="redis-cli ping" --health-interval=10s --health-timeout=5s --health-retries=3
      nacos:
        image: nacos/nacos-server:v2.4.2
        ports:
          - 8848:8848
        env:
          MODE: standalone
          SPRING_SECURITY_ENABLED: false
        options: --health-cmd="curl -f http://localhost:8848/nacos" --health-interval=10s --health-timeout=5s --health-retries=3 --health-start-period=30s
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        java: [ 8, 17, 21 ]
    steps:
      # step 1
      - name: "Checkout"
        uses: actions/checkout@v3
      # step 2
      - name: "Use Python 3.x"
        uses: actions/setup-python@v2
        with:
          python-version: '3.12'
      # step 3
      - name: "Set up Java JDK"
        uses: actions/setup-java@v3.12.0
        with:
          distribution: 'zulu'
          java-version: ${{ matrix.java }}
      # step 4
      - name: "Print maven version"
        run: ./mvnw -version
      # step 5
      - name: "Restore local maven repository cache"
        uses: actions/cache/restore@v4
        id: cache-maven-repository
        with:
         path: ~/.m2/repository
         key: ${{ runner.os }}-maven-${{ hashFiles('**/pom.xml') }}-${{ env.TODAY }}
         restore-keys: |
           ${{ runner.os }}-maven-${{ hashFiles('**/pom.xml') }}
           ${{ runner.os }}-maven-
      # step 6.1
      - name: "Test, Check style, Check PMD, Check license with Maven and Java8"
        if: matrix.java == '8'
        run: |
          ./mvnw -T 4C clean test \
                 -Dcheckstyle.skip=false -Dpmd.skip=false -Dlicense.skip=false -DredisCaseEnabled=true \
                 -e -B \
                 -Dorg.slf4j.simpleLogger.log.org.apache.maven.cli.transfer.Slf4jMavenTransferListener=warn \
                 -Dorg.slf4j.simpleLogger.log.net.sourceforge.pmd=error;
      # step 6.2
      - name: "Test with Maven and Java${{ matrix.java }}"
        if: matrix.java != '8'
        run: |
          ./mvnw -T 4C clean test \
                 -e -B -Dorg.slf4j.simpleLogger.log.org.apache.maven.cli.transfer.Slf4jMavenTransferListener=warn;
      # step 7
      - name: "Save local maven repository cache"
        uses: actions/cache/save@v4
        if: steps.cache-maven-repository.outputs.cache-hit != 'true'
        with:
          path: ~/.m2/repository
          key: ${{ runner.os }}-maven-${{ hashFiles('**/pom.xml') }}-${{ env.TODAY }}
      # step 8
      - name: "Codecov"
        if: matrix.java == '8'
        uses: codecov/codecov-action@v4.0.1
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
          version: v0.6.0
        env:
          CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}

  # job 2: Build on 'arm64v8/ubuntu' OS (Skip tests).
  build_arm64-binary:
    runs-on: ubuntu-latest
    if: ${{ github.event_name == 'push' && (github.ref_name == 'develop' || github.ref_name == 'snapshot' || github.ref_name == '2.x') }}
    strategy:
      fail-fast: false
    steps:
      # step 1
      - name: "Checkout"
        uses: actions/checkout@v3
      # step 2
      - name: "Set up QEMU"
        id: qemu
        uses: docker/setup-qemu-action@v3
        with:
          platforms: arm64
      - name: "Set up Docker Buildx"
        uses: docker/setup-buildx-action@v3
      # step 3
      - name: "Build with Maven on 'arm64v8/ubuntu:20.04' OS (Skip tests)"
        run: |
          docker run --rm -v ${{ github.workspace }}:/ws:rw --workdir=/ws \
            --platform linux/arm64 arm64v8/ubuntu:20.04 \
            bash -exc 'apt-get update -y && \
                       apt-get install maven -y && \
                       apt-get install -y python3 python3-pip python3-distutils && \
                       apt-get install -y build-essential && \
                       mvn -version && \
                       mvn clean install \
                           -Prelease-seata \
                           -DskipTests \
                           -e -B -Dorg.slf4j.simpleLogger.log.org.apache.maven.cli.transfer.Slf4jMavenTransferListener=warn'
