#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
name: "test-druid"

on:
  push:
    branches: [ test*, "*.*.*" ]

jobs:
  test-druid:
    services:
      redis:
        image: redis:7.2
        ports:
          - 6379:6379
        options: --health-cmd="redis-cli ping" --health-interval=10s --health-timeout=5s --health-retries=3
      nacos:
        image: nacos/nacos-server:v2.4.2
        ports:
          - 8848:8848
        env:
          MODE: standalone
          SPRING_SECURITY_ENABLED: false
        options: --health-cmd="curl -f http://localhost:8848/nacos" --health-interval=10s --health-timeout=5s --health-retries=3 --health-start-period=30s
    name: "test-druid"
    runs-on: ubuntu-24.04
    strategy:
      fail-fast: false
      matrix:
        druid: [
          1.2.20,
          1.2.19,
          #1.2.18, # Unit test triggered a bug in Druid, see the commit https://github.com/alibaba/druid/commit/6c493f852852fb287ed5fd31ee16c27ead0ea5cf
          #1.2.17, # Unit test triggered a bug in Druid, see the commit https://github.com/alibaba/druid/commit/6c493f852852fb287ed5fd31ee16c27ead0ea5cf
          1.2.16,
          1.2.15,
          1.2.14,
          1.2.13,
          1.2.12,
          1.2.11,
          1.2.10,
          1.2.9,
          1.2.8,
          1.2.7,
          1.2.6,
          1.2.5,
          1.2.4,
          1.2.3,
          1.2.2,
          1.2.1,
          1.2.0,

          # not support druid:1.1.x
          #1.1.24,
          #1.1.23,
          #1.1.22,
          #1.1.21,
          #1.1.20,
        ]
    steps:
      # step 1
      - name: "Checkout"
        uses: actions/checkout@v3
      # step 2
      - name: "Set up Java JDK"
        uses: actions/setup-java@v3.12.0
        with:
          distribution: 'zulu'
          java-version: 8
      # step 3
      - name: "Print maven version"
        run: ./mvnw -version
      # step 4
      - name: "Test with Maven and Druid ${{ matrix.druid }}"
        run: |
          ./mvnw -T 4C clean test \
                 -Ddruid.version=${{ matrix.druid }} \
                 -e -B -Dorg.slf4j.simpleLogger.log.org.apache.maven.cli.transfer.Slf4jMavenTransferListener=warn;
