#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
name: "Publish Docker Image"

on:
  push:
    branches: [ snapshot, "*.*.*" ]
    #tags: [ "*" ]

  #This schedule only takes effect in the default branch
  schedule:
    - cron: '0 16 * * *' #GMT+0

jobs:
  # job 1
  publish-images-to-dockerhub:
    name: "Publish images to DockerHub"
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        java: [ 8, 17, 21 ]
    steps:
      # step 1
      - name: "Checkout"
        uses: actions/checkout@v3
      # step 2
      - name: "Setup Java JDK"
        uses: actions/setup-java@v3.12.0
        with:
          distribution: 'zulu'
          java-version: ${{ matrix.java }}
      # step 3
      - name: "Print maven version"
        run: ./mvnw -version
      # step 4 based on java8
      - name: "Publish images to DockerHub based on java8"
        if: matrix.java == 8
        env:
          REGISTRY_USERNAME: ${{ secrets.DOCKERHUB_USER }}
          REGISTRY_PASSWORD: ${{ secrets.DOCKERHUB_TOKEN }}
        run: |
          if [ "${{ github.ref_name }}" == "develop" ] || [ "${{ github.ref_name }}" == "snapshot"  || [ "${{ github.ref_name }}" == "2.x" ]; then
            ./mvnw -T 4C clean package -Dimage.name=eclipse-temurin:8u422-b05-jdk      -Pimage                                    -DskipTests -e -B -Dorg.slf4j.simpleLogger.log.org.apache.maven.cli.transfer.Slf4jMavenTransferListener=warn;
          else
            ./mvnw -T 4C clean package -Dimage.name=eclipse-temurin:8u422-b05-jdk      -Pimage,release-image-based-on-java8       -DskipTests -e -B -Dorg.slf4j.simpleLogger.log.org.apache.maven.cli.transfer.Slf4jMavenTransferListener=warn;
          fi
      # step 4 based on java17
      - name: "Publish images to DockerHub based on java17"
        if: ${{ matrix.java == 17 && github.ref_name != 'develop' && github.ref_name != 'snapshot' && github.ref_name != '2.x' }}
        env:
          REGISTRY_USERNAME: ${{ secrets.DOCKERHUB_USER }}
          REGISTRY_PASSWORD: ${{ secrets.DOCKERHUB_TOKEN }}
        run: |
          ./mvnw -T 4C clean package -Dimage.name=eclipse-temurin:17.0.12_7-jdk       -Pimage,release-image-based-on-java17      -DskipTests -e -B -Dorg.slf4j.simpleLogger.log.org.apache.maven.cli.transfer.Slf4jMavenTransferListener=warn;
      # step 5 based on java21
      - name: "Publish images to DockerHub based on java21"
        if: ${{ matrix.java == 21 && github.ref_name != 'develop' && github.ref_name != 'snapshot' && github.ref_name != '2.x' }}
        env:
          REGISTRY_USERNAME: ${{ secrets.DOCKERHUB_USER }}
          REGISTRY_PASSWORD: ${{ secrets.DOCKERHUB_TOKEN }}
        run: |
          ./mvnw -T 4C clean package -Dimage.name=eclipse-temurin:21.0.4_7-jdk       -Pimage,release-image-based-on-java21      -DskipTests -e -B -Dorg.slf4j.simpleLogger.log.org.apache.maven.cli.transfer.Slf4jMavenTransferListener=warn;
