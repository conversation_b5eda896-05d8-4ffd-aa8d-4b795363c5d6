#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
name: "test-ubuntu"

on:
  push:
    branches: [ test*, "*.*.*" ]
jobs:
  # job 1
  test:
    name: "test"
    services:
      redis:
        image: redis:7.2
        ports:
          - 6379:6379
        options: --health-cmd="redis-cli ping" --health-interval=10s --health-timeout=5s --health-retries=3
      nacos:
        image: nacos/nacos-server:v2.4.2
        ports:
          - 8848:8848
        env:
          MODE: standalone
          SPRING_SECURITY_ENABLED: false
        options: --health-cmd="curl -f http://localhost:8848/nacos" --health-interval=10s --health-timeout=5s --health-retries=3 --health-start-period=30s
    runs-on: "${{ matrix.os }}"
    strategy:
      max-parallel: 3
      fail-fast: false
      matrix:
        java: [ 8, 11, 17, 21 ]
        os: [
          ubuntu-24.04,
        ]
        springboot: [
          2.7.18         -D spring-framework.version=5.3.31,
          2.6.15         -D spring-framework.version=5.3.27,
          2.5.15         -D spring-framework.version=5.3.27,
          2.4.13         -D spring-framework.version=5.3.13,
          2.3.12.RELEASE -D spring-framework.version=5.2.15.RELEASE,
#          2.2.13.RELEASE -D spring-framework.version=5.2.12.RELEASE,
          #2.1.18.RELEASE,
          #2.0.9.RELEASE,
        ]
    steps:
      # step 1
      - name: "Checkout"
        uses: actions/checkout@v3
      # step 2
      - name: "Use Python 3.x"
        uses: actions/setup-python@v2
        with:
          python-version: '3.12'
      # step 3
      - name: "Set up Java JDK"
        uses: actions/setup-java@v3.12.0
        with:
          distribution: 'zulu'
          java-version: ${{ matrix.java }}
      # step 4
      ## step 4.1: for Ubuntu and MacOS
      - name: "Test with Maven on '${{ matrix.os }}' OS"
        run: |
          ./mvnw -T 4C clean test -P args-for-client-test -Dspring-boot.version=${{ matrix.springboot }} -e -B -Dorg.slf4j.simpleLogger.log.org.apache.maven.cli.transfer.Slf4jMavenTransferListener=warn;

  # job 2
  test-springboot3x:
    name: "test-springboot3.x"
    services:
      redis:
        image: redis:7.2
        ports:
          - 6379:6379
        options: --health-cmd="redis-cli ping" --health-interval=10s --health-timeout=5s --health-retries=3
      nacos:
        image: nacos/nacos-server:v2.4.2
        ports:
          - 8848:8848
        env:
          MODE: standalone
          SPRING_SECURITY_ENABLED: false
        options: --health-cmd="curl -f http://localhost:8848/nacos" --health-interval=10s --health-timeout=5s --health-retries=3 --health-start-period=30s
    runs-on: "${{ matrix.os }}"
    strategy:
      max-parallel: 3
      fail-fast: false
      matrix:
        java: [ 17, 21 ]
        os: [
          ubuntu-24.04,
        ]
        springboot: [
          3.3.0  -D spring-framework.version=6.1.18 -D mockito.version=5.11.0 -D junit-jupiter.version=5.10.2,
          3.2.0  -D spring-framework.version=6.1.1  -D mockito.version=5.7.0  -D junit-jupiter.version=5.10.1,
          3.1.6  -D spring-framework.version=6.0.14 -D mockito.version=5.3.1  -D junit-jupiter.version=5.9.3,
          3.0.13 -D spring-framework.version=6.0.14,
        ]
    steps:
      # step 1
      - name: "Checkout"
        uses: actions/checkout@v3
      # step 2
      - name: "Use Python 3.x"
        uses: actions/setup-python@v2
        with:
          python-version: '3.12'
      # step 3
      - name: "Set up Java JDK"
        uses: actions/setup-java@v3.12.0
        with:
          distribution: 'zulu'
          java-version: ${{ matrix.java }}
      # step 4
      ## step 4.1: for Ubuntu and MacOS
      - name: "Test with Maven on '${{ matrix.os }}' OS"
        if: ${{ ! startsWith(matrix.os, 'windows') }}
        run: |
          ./mvnw -T 4C clean test -P args-for-client-test -Dspring-boot.version=${{ matrix.springboot }} -e -B -Dorg.slf4j.simpleLogger.log.org.apache.maven.cli.transfer.Slf4jMavenTransferListener=warn;
      ## step 4.2: for Windows
      - name: "Build with Maven on 'windows' OS (Skip tests)"
        if: ${{ startsWith(matrix.os, 'windows') }}
        run: | # Skip tests, because too many errors in unit-test.
          ./mvnw.cmd -version;
          ./mvnw.cmd clean install -P args-for-client-test -DskipTests -D spring-boot.version=${{ matrix.springboot }} -e -B -D org.slf4j.simpleLogger.log.org.apache.maven.cli.transfer.Slf4jMavenTransferListener=warn;

  # job 3
  test-arm64:
    name: "test-arm64"
    runs-on: ubuntu-24.04
    strategy:
      fail-fast: false
      matrix:
        springboot: [
          #2.7.18         -Dspring-framework.version=5.3.31, # The maven-compiler-plugin will throw an error for an unknown reason.
          #2.6.15         -Dspring-framework.version=5.3.27, # The maven-compiler-plugin will throw an error for an unknown reason.
          #2.5.15         -Dspring-framework.version=5.3.27, # The maven-compiler-plugin will throw an error for an unknown reason.
          2.4.13         -Dspring-framework.version=5.3.13,
          2.3.12.RELEASE -Dspring-framework.version=5.2.15.RELEASE,
          2.2.13.RELEASE -Dspring-framework.version=5.2.12.RELEASE,
          #2.1.18.RELEASE,
          #2.0.9.RELEASE,
        ]
    steps:
      # step 1
      - name: "Checkout"
        uses: actions/checkout@v3
      # step 2
      - name: "Set up QEMU"
        id: qemu
        uses: docker/setup-qemu-action@v3
      # step 3
      - name: "Build with Maven on 'arm64v8/ubuntu:20.04' OS (Skip tests)"
        run: |
          docker run --rm -v ${{ github.workspace }}:/ws:rw --workdir=/ws \
            --platform linux/arm64 arm64v8/ubuntu:20.04 \
            bash -exc 'apt-get update -y && \
                       apt-get install maven -y && \
                       apt-get install -y python3 python3-pip python3-distutils && \
                       apt-get install -y build-essential && \
                       mvn -version && \
                       mvn -T 4C clean install \
                           -Dspring-boot.version=${{ matrix.springboot }} \
                           -Prelease-seata \
                           -DskipTests \
                           -e -B -Dorg.slf4j.simpleLogger.log.org.apache.maven.cli.transfer.Slf4jMavenTransferListener=warn'
