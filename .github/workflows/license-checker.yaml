#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
name: License checker

on:
  pull_request:
      branches: [ 2.x, develop, master ]

jobs:
  check-license:
    runs-on: ubuntu-latest
    steps:
      # step 1 clear cache
      - name: Clear cache directory first before trying to restore from cache
        run: sudo rm -rf $(go env GOMODCACHE) && sudo rm -rf $(go env GOCACHE)
        shell: bash
      # step 2 checkout
      - name: Checkout
        uses: actions/checkout@v3
      # step 3 https://github.blog/changelog/2022-09-22-github-actions-all-actions-will-begin-running-on-node16-instead-of-node12/
      - uses: actions/setup-node@v3
        with:
          node-version: '14.x'
      # step 4 check license
      - name: Check License Header
        uses: apache/skywalking-eyes/header@8fc52baabc14c86294d96034bcc194cfa7f76b05
        with:
          log: info
          config: .licenserc.yaml
          mode: check
      # step 5 check dependencies
      - name: Check Dependencies' License
        uses: apache/skywalking-eyes/dependency@8fc52baabc14c86294d96034bcc194cfa7f76b05
        with:
          log: info
          config: .licenserc.yaml
          mode: check