#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
name: "CodeQL"

on:
  pull_request:
    # The branches below must be a subset of the branches above
    branches: [ 2.x, develop ]
  schedule:
    - cron: '36 19 * * 6'

jobs:
  analyze:
    name: Analyze
    runs-on: ubuntu-latest
    permissions:
      actions: read
      contents: read
      security-events: write
    strategy:
      fail-fast: false
      matrix:
        language: [ 'java' ]

    steps:
      # step 1
      - name: "Checkout"
        uses: actions/checkout@v3

      # step 2: Initializes the CodeQL tools for scanning.
      - name: "Initialize CodeQL"
        uses: github/codeql-action/init@v3
        with:
          languages: ${{ matrix.language }}

      - run: ./mvnw -T 4C clean install -DskipTests

      # step 3
      - name: "Perform CodeQL Analysis"
        uses: github/codeql-action/analyze@v3
        with:
          category: "/language:${{matrix.language}}"