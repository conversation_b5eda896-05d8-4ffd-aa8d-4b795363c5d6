#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
name: "test"

on:
  push:
    branches: [ test*, "*.*.*" ]
jobs:
  # job 1
  test:
    name: "test"
    runs-on: "${{ matrix.os }}"
    strategy:
      max-parallel: 3
      fail-fast: false
      matrix:
        java: [ 8, 11, 17, 21 ]
        os: [
          macos-14,
          windows-2022, # Skip tests, because too many errors in unit-test.
        ]
        springboot: [
          2.7.18         -D spring-framework.version=5.3.31,
          2.6.15         -D spring-framework.version=5.3.27,
          2.5.15         -D spring-framework.version=5.3.27,
          2.4.13         -D spring-framework.version=5.3.13,
          2.3.12.RELEASE -D spring-framework.version=5.2.15.RELEASE,
#          2.2.13.RELEASE -D spring-framework.version=5.2.12.RELEASE,
          #2.1.18.RELEASE,
          #2.0.9.RELEASE,
        ]
    steps:
      # step 1
      - name: "Checkout"
        uses: actions/checkout@v3
      # step 2
      - name: "Use Python 3.x"
        uses: actions/setup-python@v2
        with:
          python-version: '3.12'
      # step 3
      - name: "Set up Java JDK"
        uses: actions/setup-java@v3.12.0
        with:
          distribution: 'zulu'
          java-version: ${{ matrix.java }}
      # step 4
      ## step 4.1: for Ubuntu and MacOS
      - name: "Test with Maven on '${{ matrix.os }}' OS"
        if: ${{ ! startsWith(matrix.os, 'windows') }}
        run: |
          if [[ "${{ matrix.os }}" =~ "macos" ]]; then
             python3 -m ensurepip --upgrade;
             python3 -m pip install --upgrade pip setuptools;
             if ! python3 -c "import distutils" &>/dev/null; then
                python3 -m pip install --upgrade setuptools
             fi
            ./mvnw -T 4C clean test -P args-for-client-test  -P arrch64 -Dspring-boot.version=${{ matrix.springboot }} -e -B -Dorg.slf4j.simpleLogger.log.org.apache.maven.cli.transfer.Slf4jMavenTransferListener=warn;
          else
            ./mvnw -T 4C clean test -P args-for-client-test -Dspring-boot.version=${{ matrix.springboot }} -e -B -Dorg.slf4j.simpleLogger.log.org.apache.maven.cli.transfer.Slf4jMavenTransferListener=warn;
          fi
      ## step 4.2: for Windows
      - name: "Build with Maven on 'windows' OS (Skip tests)"
        if: ${{ startsWith(matrix.os, 'windows') }}
        run: | # Skip tests, because too many errors in unit-test.
          ./mvnw.cmd -version;
          ./mvnw.cmd clean install -P args-for-client-test -DskipTests -D spring-boot.version=${{ matrix.springboot }} -e -B -D org.slf4j.simpleLogger.log.org.apache.maven.cli.transfer.Slf4jMavenTransferListener=warn;

  # job 2
  test-springboot3x:
    name: "test-springboot3.x"
    runs-on: "${{ matrix.os }}"
    strategy:
      max-parallel: 3
      fail-fast: false
      matrix:
        java: [ 17, 21 ]
        os: [
          macos-14,
          windows-2022, # Skip tests, because too many errors in unit-test.
        ]
        springboot: [
          3.3.0  -D spring-framework.version=6.1.18 -D mockito.version=5.11.0 -D junit-jupiter.version=5.10.2,
          3.2.0  -D spring-framework.version=6.1.1  -D mockito.version=5.7.0  -D junit-jupiter.version=5.10.1,
          3.1.6  -D spring-framework.version=6.0.14 -D mockito.version=5.3.1  -D junit-jupiter.version=5.9.3,
          3.0.13 -D spring-framework.version=6.0.14,
        ]
    steps:
      # step 1
      - name: "Checkout"
        uses: actions/checkout@v3
      # step 2
      - name: "Use Python 3.x"
        uses: actions/setup-python@v2
        with:
          python-version: '3.12'
      # step 3
      - name: "Set up Java JDK"
        uses: actions/setup-java@v3.12.0
        with:
          distribution: 'zulu'
          java-version: ${{ matrix.java }}
      # step 4
      ## step 4.1: for Ubuntu and MacOS
      - name: "Test with Maven on '${{ matrix.os }}' OS"
        if: ${{ ! startsWith(matrix.os, 'windows') }}
        run: |
          if [[ "${{ matrix.os }}" =~ "macos" ]]; then
             python3 -m ensurepip --upgrade;
             python3 -m pip install --upgrade pip setuptools;
             if ! python3 -c "import distutils" &>/dev/null; then
                python3 -m pip install --upgrade setuptools
             fi
            ./mvnw -T 4C clean test -P args-for-client-test  -P arrch64 -Dspring-boot.version=${{ matrix.springboot }} -e -B -Dorg.slf4j.simpleLogger.log.org.apache.maven.cli.transfer.Slf4jMavenTransferListener=warn;
          else
            ./mvnw -T 4C clean test -P args-for-client-test -Dspring-boot.version=${{ matrix.springboot }} -e -B -Dorg.slf4j.simpleLogger.log.org.apache.maven.cli.transfer.Slf4jMavenTransferListener=warn;
          fi
      ## step 4.2: for Windows
      - name: "Build with Maven on 'windows' OS (Skip tests)"
        if: ${{ startsWith(matrix.os, 'windows') }}
        run: | # Skip tests, because too many errors in unit-test.
          ./mvnw.cmd -version;
          ./mvnw.cmd clean install -P args-for-client-test -DskipTests -D spring-boot.version=${{ matrix.springboot }} -e -B -D org.slf4j.simpleLogger.log.org.apache.maven.cli.transfer.Slf4jMavenTransferListener=warn;
