<?xml version="1.0" encoding="UTF-8"?>
<!--

    Licensed to the Apache Software Foundation (ASF) under one or more
    contributor license agreements.  See the NOTICE file distributed with
    this work for additional information regarding copyright ownership.
    The ASF licenses this file to You under the Apache License, Version 2.0
    (the "License"); you may not use this file except in compliance with
    the License.  You may obtain a copy of the License at

        http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.

-->
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:jdbc="http://www.springframework.org/schema/jdbc"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd

       http://www.springframework.org/schema/jdbc http://www.springframework.org/schema/jdbc/spring-jdbc.xsd">

	<bean id="dataSource" class="org.h2.jdbcx.JdbcConnectionPool" destroy-method="dispose">
		<constructor-arg>
			<bean class="org.h2.jdbcx.JdbcDataSource">
				<property name="URL" value="jdbc:h2:mem:seata_saga" />
				<property name="user" value="sa" />
				<property name="password" value="sa" />
			</bean>
		</constructor-arg>
	</bean>

	<!-- 初始化数据表结构 -->
	<jdbc:initialize-database data-source="dataSource">
		<jdbc:script location="classpath:saga/sql/h2_init.sql" />
	</jdbc:initialize-database>

	<bean id="stateMachineEngine" class="io.seata.saga.engine.impl.ProcessCtrlStateMachineEngine">
		<property name="stateMachineConfig" ref="dbStateMachineConfig"/>
	</bean>
	<bean id="dbStateMachineConfig" class="io.seata.saga.engine.config.DbStateMachineConfig">
		<property name="dataSource" ref="dataSource"/>
		<property name="resources" value="saga/statelang/*.json"/>
		<property name="enableAsync" value="true"/>
		<property name="threadPoolExecutor" ref="threadExecutor"/>
		<property name="applicationId" value="test_saga"/>
		<property name="txServiceGroup" value="default_tx_group"/>
		<property name="sagaBranchRegisterEnable" value="false"/>
		<property name="sagaJsonParser" value="jackson"/>
		<property name="sagaRetryPersistModeUpdate" value="false" />
		<property name="sagaCompensatePersistModeUpdate" value="false" />
	</bean>

	<bean id="threadExecutor"
		  class="org.springframework.scheduling.concurrent.ThreadPoolExecutorFactoryBean">
		<property name="threadNamePrefix" value="SAGA_ASYNC_EXE_" />
		<property name="corePoolSize" value="20" />
		<property name="maxPoolSize" value="20" />
		<property name="queueCapacity" value="100" />
		<property name="rejectedExecutionHandler" ref="callerRunsPolicy" />
	</bean>

	<bean name="callerRunsPolicy" class="java.util.concurrent.ThreadPoolExecutor.CallerRunsPolicy">
	</bean>


	<bean id="demoService" class="io.seata.saga.engine.mock.DemoService"/>
</beans>