{"Name": "simpleScriptTaskStateMachine", "Comment": "带ScriptTask的测试状态机定义", "StartState": "FirstState", "Version": "0.0.1", "States": {"FirstState": {"Type": "ServiceTask", "ServiceName": "demoService", "ServiceMethod": "foo", "Next": "ScriptState", "Input": [{"fooInput": "$.[a]"}], "Output": {"fooResult": "$.#root"}}, "ScriptState": {"Type": "ScriptTask", "ScriptType": "groovy", "ScriptContent": "if(throwException){ throw new RuntimeException(\"test\") } else { 'hello ' + inputA }", "Input": [{"inputA": "$.[a]", "throwException": "$.[scriptThrowException]"}], "Output": {"scriptStateResult": "$.#root"}, "Catch": [{"Exceptions": ["java.lang.Throwable"], "Next": "Fail"}], "Next": "ChoiceState"}, "ChoiceState": {"Type": "Choice", "Choices": [{"Expression": "[a] == 1", "Next": "SecondState"}, {"Expression": "[a] == 2", "Next": "ThirdState"}], "Default": "Fail"}, "SecondState": {"Type": "ServiceTask", "ServiceName": "demoService", "ServiceMethod": "bar", "Input": [{"barInput": "$.[fooResult]", "throwException": "$.[barThrowException]"}], "Output": {"barResult": "$.#root"}, "Catch": [{"Exceptions": ["io.seata.saga.engine.mock.DemoException"], "Next": "Fail"}], "Next": "Succeed"}, "ThirdState": {"Type": "ServiceTask", "ServiceName": "demoService", "ServiceMethod": "foo", "Input": [{"fooInput": "$.[fooResult]"}], "Output": {"fooResult": "$.#root"}, "Next": "Succeed"}, "Succeed": {"Type": "Succeed"}, "Fail": {"Type": "Fail", "ErrorCode": "NOT_FOUND", "Message": "not found"}}}