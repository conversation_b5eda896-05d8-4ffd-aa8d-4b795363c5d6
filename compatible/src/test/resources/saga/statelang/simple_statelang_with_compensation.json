{"Name": "simpleCompensationStateMachine", "Comment": "带补偿定义的测试状态机定义", "StartState": "FirstState", "Version": "0.0.1", "States": {"FirstState": {"Type": "ServiceTask", "ServiceName": "demoService", "ServiceMethod": "foo", "CompensateState": "CompensateFirstState", "Next": "ChoiceState", "Input": [{"fooInput": "$.[a]", "throwException": "$.[fooThrowException]", "sleepTime": "$.[fooSleepTime]"}], "Output": {"fooResult": "$.#root"}, "Status": {"$Exception{io.seata.saga.engine.mock.DemoException}": "UN", "#root != null": "SU", "#root == null": "FA"}, "Catch": [{"Exceptions": ["io.seata.saga.engine.mock.DemoException"], "Next": "CompensationTrigger"}]}, "ChoiceState": {"Type": "Choice", "Choices": [{"Expression": "[a] == 1", "Next": "SecondState"}, {"Expression": "[a] == 2", "Next": "ThirdState"}], "Default": "Fail"}, "SecondState": {"Type": "ServiceTask", "ServiceName": "demoService", "ServiceMethod": "bar", "CompensateState": "CompensateSecondState", "Input": [{"barInput": "$.[fooResult]", "throwException": "$.[barThrowException]", "sleepTime": "$.[barSleepTime]"}], "Output": {"barResult": "$.#root"}, "Status": {"$Exception{io.seata.saga.engine.mock.DemoException}": "UN", "#root != null": "SU", "#root == null": "FA"}, "Catch": [{"Exceptions": ["io.seata.saga.engine.mock.DemoException"], "Next": "CompensationTrigger"}], "Next": "Succeed"}, "ThirdState": {"Type": "ServiceTask", "ServiceName": "demoService", "ServiceMethod": "foo", "Input": [{"fooInput": "$.[fooResult]"}], "Output": {"fooResult": "$.#root"}, "Next": "Succeed"}, "CompensateFirstState": {"Type": "ServiceTask", "ServiceName": "demoService", "ServiceMethod": "compensateFoo", "Input": [{"compensateFooInput": "$.[fooResult]", "throwException": "$.[compensateFooThrowException]"}]}, "CompensateSecondState": {"Type": "ServiceTask", "ServiceName": "demoService", "ServiceMethod": "compensateBar", "Input": [{"compensateBarInput": "$.[barR<PERSON>ult]", "throwException": "$.[compensateBarThrowException]"}]}, "CompensationTrigger": {"Type": "CompensationTrigger", "Next": "Fail"}, "Succeed": {"Type": "Succeed"}, "Fail": {"Type": "Fail", "ErrorCode": "NOT_FOUND", "Message": "not found"}}}