{"Name": "simpleStateMachineWithCompensationAndSubMachine", "Comment": "带补偿定义和调用子状态机", "StartState": "FirstState", "Version": "0.0.1", "IsRetryPersistModeUpdate": false, "IsCompensatePersistModeUpdate": false, "States": {"FirstState": {"Type": "ServiceTask", "ServiceName": "demoService", "ServiceMethod": "foo", "CompensateState": "CompensateFirstState", "Next": "ChoiceState", "Input": [{"fooInput": "$.[a]"}], "Output": {"fooResult": "$.#root"}}, "ChoiceState": {"Type": "Choice", "Choices": [{"Expression": "[a] == 1", "Next": "SecondState"}, {"Expression": "[a] == 2", "Next": "CallSubStateMachine"}, {"Expression": "[a] == 3 || [a] == 4", "Next": "CallSubStateMachineAsUpdate"}], "Default": "Fail"}, "SecondState": {"Type": "ServiceTask", "ServiceName": "demoService", "ServiceMethod": "bar", "Input": [{"barInput": "$.[fooResult]", "throwException": "$.[barThrowException]"}], "Output": {"barResult": "$.#root"}, "Status": {"#root != null": "SU", "#root == null": "FA", "$Exception{io.seata.saga.engine.mock.DemoException}": "UN"}, "Catch": [{"Exceptions": ["io.seata.saga.engine.mock.DemoException"], "Next": "CompensationTrigger"}], "Next": "Succeed"}, "CallSubStateMachine": {"Type": "SubStateMachine", "StateMachineName": "simpleCompensationStateMachine", "Input": [{"a": "$.1", "barThrowException": "$.[barThrowException]", "fooThrowException": "$.[fooThrowException]", "compensateFooThrowException": "$.[compensateFooThrowException]"}], "Output": {"fooResult": "$.#root"}, "Next": "Succeed"}, "CallSubStateMachineAsUpdate": {"Type": "SubStateMachine", "StateMachineName": "simpleUpdateStateMachine", "IsRetryPersistModeUpdate": true, "IsCompensatePersistModeUpdate": true, "Input": [{"a": "$.[a]-2", "barThrowException": "$.[barThrowException]", "compensateBarThrowException": "$.[compensateBarThrowException]"}], "Output": {"fooResult": "$.#root"}, "Next": "Succeed"}, "CompensateFirstState": {"Type": "ServiceTask", "ServiceName": "demoService", "ServiceMethod": "compensateFoo", "Input": [{"compensateFooInput": "$.[fooResult]"}]}, "CompensationTrigger": {"Type": "CompensationTrigger", "Next": "Fail"}, "Succeed": {"Type": "Succeed"}, "Fail": {"Type": "Fail", "ErrorCode": "NOT_FOUND", "Message": "not found"}}}