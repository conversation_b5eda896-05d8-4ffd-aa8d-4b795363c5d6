{"Name": "simpleUpdateStateMachine", "Comment": "自定义中间状态是否持久化的测试状态机定义", "StartState": "FirstState", "Version": "0.0.1", "IsRetryPersistModeUpdate": true, "IsCompensatePersistModeUpdate": true, "States": {"FirstState": {"Type": "ServiceTask", "ServiceName": "demoService", "ServiceMethod": "foo", "Next": "ChoiceState", "CompensateState": "CompensateFirstState", "Input": [{"fooInput": "$.[a]"}], "Output": {"fooResult": "$.#root"}}, "ChoiceState": {"Type": "Choice", "Choices": [{"Expression": "[a] == 1", "Next": "SecondState"}, {"Expression": "[a] == 2", "Next": "ThirdState"}], "Default": "Fail"}, "SecondState": {"Type": "ServiceTask", "ServiceName": "demoService", "ServiceMethod": "bar", "Input": [{"barInput": "$.[fooResult]", "throwException": "$.[barThrowException]"}], "Output": {"barResult": "$.#root"}, "Status": {"$Exception{java.lang.Throwable}": "UN", "#root != null": "SU", "#root == null": "FA"}, "Next": "Succeed"}, "ThirdState": {"Type": "ServiceTask", "ServiceName": "demoService", "ServiceMethod": "randomExceptionMethod", "CompensateState": "CompensateThirdState", "Input": [{"barInput": "$.[fooResult]", "throwException": "$.[barThrowException]"}], "Output": {"barResult": "$.#root"}, "Status": {"$Exception{java.lang.Throwable}": "UN", "#root != null": "SU", "#root == null": "FA"}, "Catch": [{"Exceptions": ["java.lang.Throwable"], "Next": "CompensationTrigger"}], "Next": "Succeed"}, "CompensateFirstState": {"Type": "ServiceTask", "ServiceName": "demoService", "ServiceMethod": "compensateFoo"}, "CompensateThirdState": {"Type": "ServiceTask", "ServiceName": "demoService", "ServiceMethod": "compensateBar", "Input": [{"compensateBarInput": "$.[barR<PERSON>ult]", "throwException": "$.[compensateBarThrowException]"}]}, "CompensationTrigger": {"Type": "CompensationTrigger", "Next": "Fail"}, "Succeed": {"Type": "Succeed"}, "Fail": {"Type": "Fail", "ErrorCode": "NOT_FOUND", "Message": "not found"}}}