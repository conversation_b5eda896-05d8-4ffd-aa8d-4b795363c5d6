{"Name": "simpleStateMachineWithRecoverStrategy", "Comment": "带自定义恢复策略的测试状态机定义", "StartState": "FirstState", "Version": "0.0.1", "RecoverStrategy": "Forward", "States": {"FirstState": {"Type": "ServiceTask", "ServiceName": "demoService", "ServiceMethod": "foo", "Input": [{"fooInput": "$.[a]", "throwExceptionRandomly": "$.[fooThrowExceptionRandomly]", "sleepTime": "$.[fooSleepTime]"}], "Output": {"fooResult": "$.#root"}, "Status": {"$Exception{io.seata.saga.engine.mock.DemoException}": "UN", "#root != null": "SU", "#root == null": "FA"}, "Catch": [{"Exceptions": ["java.lang.Throwable"], "Next": "Fail"}], "Next": "ChoiceState"}, "ChoiceState": {"Type": "Choice", "Choices": [{"Expression": "[a] == 1", "Next": "SecondState"}, {"Expression": "[a] == 2", "Next": "Fail"}], "Default": "Fail"}, "SecondState": {"Type": "ServiceTask", "ServiceName": "demoService", "ServiceMethod": "bar", "Input": [{"barInput": "$.[fooResult]", "throwExceptionRandomly": "$.[barThrowExceptionRandomly]", "sleepTime": "$.[barSleepTime]"}], "Output": {"barResult": "$.#root"}, "Status": {"$Exception{io.seata.saga.engine.mock.DemoException}": "UN", "#root != null": "SU", "#root == null": "FA"}, "Catch": [{"Exceptions": ["java.lang.Throwable"], "Next": "Fail"}], "Next": "Succeed"}, "Succeed": {"Type": "Succeed"}, "Fail": {"Type": "Fail", "ErrorCode": "NOT_FOUND", "Message": "not found"}}}