/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package io.seata.rm.datasource.mock;

import java.sql.DatabaseMetaData;
import java.sql.SQLException;
import java.sql.Savepoint;
import java.util.Properties;

/**
 * Mock connection
 */
public class MockConnection extends com.alibaba.druid.mock.MockConnection {

    private MockDriver mockDriver;

    /**
     * Instantiate a new MockConnection
     * @param driver
     * @param url
     * @param connectProperties
     */
    public MockConnection(MockDriver driver, String url, Properties connectProperties) {
        super(driver, url, connectProperties);
        this.mockDriver = driver;
    }

    @Override
    public DatabaseMetaData getMetaData() throws SQLException {
        return new MockDatabaseMetaData(this);
    }

    @Override
    public void releaseSavepoint(Savepoint savepoint) throws SQLException {

    }

    @Override
    public void rollback() {

    }

    @Override
    public void rollback(Savepoint savepoint) {

    }

    @Override
    public MockDriver getDriver() {
        return mockDriver;
    }

    @Override
    public String getSchema() {
        return null;
    }
}
