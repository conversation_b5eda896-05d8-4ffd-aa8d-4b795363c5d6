/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package io.seata.integration.tx.api.interceptor.parser;

import io.seata.integration.tx.api.interceptor.handler.GlobalTransactionalInterceptorHandler;
import io.seata.tm.api.DefaultFailureHandlerImpl;
import io.seata.tm.api.FailureHandler;
import org.apache.seata.integration.tx.api.interceptor.handler.ProxyInvocationHandler;
import org.apache.seata.integration.tx.api.util.ProxyUtil;
import org.apache.seata.tm.api.FailureHandlerHolder;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

public class GlobalTransactionalInterceptorParserTest {

    @Test
    void parserInterfaceToProxy() throws Exception {

        //given
        BusinessImpl business = new BusinessImpl();

        GlobalTransactionalInterceptorParser
	        globalTransactionalInterceptorParser = new GlobalTransactionalInterceptorParser();
        FailureHandler failureHandler = new DefaultFailureHandlerImpl();

        FailureHandlerHolder.setFailureHandler(failureHandler);

        //when
        ProxyInvocationHandler proxyInvocationHandler = globalTransactionalInterceptorParser.parserInterfaceToProxy(business, business.getClass().getName());

        //then
        Assertions.assertNotNull(proxyInvocationHandler);

        Assertions.assertEquals(proxyInvocationHandler.getClass(), GlobalTransactionalInterceptorHandler.class);

        Business  businessProxy = ProxyUtil.createProxy(business);
        Assertions.assertNotEquals(businessProxy, business);

    }
}
