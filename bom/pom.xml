<?xml version="1.0" encoding="UTF-8"?>
<!--

    Licensed to the Apache Software Foundation (ASF) under one or more
    contributor license agreements.  See the NOTICE file distributed with
    this work for additional information regarding copyright ownership.
    The ASF licenses this file to You under the Apache License, Version 2.0
    (the "License"); you may not use this file except in compliance with
    the License.  You may obtain a copy of the License at

        http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.

-->
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>org.apache.seata</groupId>
        <artifactId>seata-build</artifactId>
        <version>${revision}</version>
        <relativePath>../build/pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>seata-bom</artifactId>
    <packaging>pom</packaging>

    <name>Seata bom ${project.version}</name>
    <description>Seata bom</description>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-all</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-config-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-config-custom</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-config-apollo</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-config-nacos</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-config-zk</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-config-all</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-config-etcd3</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-config-consul</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-config-spring-cloud</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-discovery-consul</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-discovery-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-discovery-custom</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-discovery-all</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-discovery-eureka</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-discovery-zk</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-discovery-namingserver</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-discovery-redis</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-discovery-nacos</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-discovery-etcd3</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-discovery-sofa</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-discovery-raft</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-brpc</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-dubbo</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-dubbo-alibaba</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-sofa-rpc</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-motan</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-grpc</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-hsf</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-http</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-rocketmq</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-rm</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-rm-datasource</artifactId>
                <version>${project.version}</version>
            </dependency>
            <!-- the 'seata-server' is an application, not a dependency
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-server</artifactId>
                <version>${project.version}</version>
            </dependency>
            -->
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-spring</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-tcc</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-tm</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-metrics-all</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-metrics-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-metrics-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-metrics-registry-compact</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-metrics-exporter-prometheus</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-serializer-all</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-serializer-protobuf</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-serializer-seata</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-serializer-kryo</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-serializer-hessian</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-spring-boot-starter</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-compressor-all</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-compressor-gzip</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-compressor-zip</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-compressor-bzip2</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-compressor-lz4</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-compressor-deflater</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-compressor-zstd</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-saga-processctrl</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-saga-statelang</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-saga-engine</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-saga-rm</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-saga-engine-store</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-saga-annotation</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-sqlparser-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-sqlparser-antlr</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-sqlparser-druid</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>apm-seata-skywalking-plugin</artifactId>
                <version>${project.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
</project>
