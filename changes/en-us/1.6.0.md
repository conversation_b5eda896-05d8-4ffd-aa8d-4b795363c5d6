<!--
    Licensed to the Apache Software Foundation (ASF) under one or more
    contributor license agreements.  See the NOTICE file distributed with
    this work for additional information regarding copyright ownership.
    The ASF licenses this file to You under the Apache License, Version 2.0
    (the "License"); you may not use this file except in compliance with
    the License.  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0
    
    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
-->
### 1.6.0

[source](https://github.com/seata/seata/archive/v1.6.0.zip) |
[binary](https://github.com/seata/seata/releases/download/v1.6.0/seata-server-1.6.0.zip)

<details>	
  <summary><mark>Release notes</mark></summary>	


### Seata 1.6.0

Seata 1.6.0 Released.

Seata is an easy-to-use, high-performance, open source distributed transaction solution.

The version is updated as follows:

### feature：
- [[#4863](https://github.com/seata/seata/pull/4863)] support oracle and postgresql multi primary key
- [[#4649](https://github.com/seata/seata/pull/4649)] seata-server support multiple registry
- [[#4779](https://github.com/seata/seata/pull/4779)] support Apache Dubbo3
- [[#4479](https://github.com/seata/seata/pull/4479)] TCC mode supports tcc annotation marked on both interface and implementation class
- [[#4877](https://github.com/seata/seata/pull/4877)] seata client support jdk17
- [[#4914](https://github.com/seata/seata/pull/4914)] support mysql update join sql
- [[#4542](https://github.com/seata/seata/pull/4542)] support oracle timestamp types
- [[#5111](https://github.com/seata/seata/pull/5111)] support Nacos contextPath
- [[#4802](https://github.com/seata/seata/pull/4802)] dockerfile support arm64


### bugfix：
- [[#4780](https://github.com/seata/seata/pull/4780)] fix can't post TimeoutRollbacked event after a successful timeout rollback
- [[#4954](https://github.com/seata/seata/pull/4954)] fix output expression incorrectly throws npe
- [[#4817](https://github.com/seata/seata/pull/4817)] fix in high version springboot property not Standard
- [[#4838](https://github.com/seata/seata/pull/4838)] fix when use Statement.executeBatch() can not generate undo log
- [[#4533](https://github.com/seata/seata/pull/4533)] fix rollback event repeated and some event status not correct
- [[#4912](https://github.com/seata/seata/pull/4912)] fix mysql InsertOnDuplicateUpdate column case is different and cannot be matched
- [[#4543](https://github.com/seata/seata/pull/4543)] fix support Oracle nclob types
- [[#4915](https://github.com/seata/seata/pull/4915)] fix failed to get server recovery properties
- [[#4919](https://github.com/seata/seata/pull/4919)] fix XID port  and  address null:0 before coordinator.init
- [[#4928](https://github.com/seata/seata/pull/4928)] fix rpcContext.getClientRMHolderMap NPE
- [[#4953](https://github.com/seata/seata/pull/4953)] fix InsertOnDuplicateUpdate bypass modify pk
- [[#4978](https://github.com/seata/seata/pull/4978)] fix kryo support circular reference
- [[#4874](https://github.com/seata/seata/pull/4874)] fix startup failure by using OpenJDK 11
- [[#5018](https://github.com/seata/seata/pull/5018)] fix loader path in startup scripts
- [[#5004](https://github.com/seata/seata/pull/5004)] fix duplicate image row for update join
- [[#5032](https://github.com/seata/seata/pull/5032)] fix mysql InsertOnDuplicateUpdate sql query error caused by placeholder index calculation error
- [[#5033](https://github.com/seata/seata/pull/5033)] fix null exception when sql columns is empty for insert on duplicate
- [[#5038](https://github.com/seata/seata/pull/5038)] remove @EnableConfigurationProperties({SagaAsyncThreadPoolProperties.class})
- [[#5050](https://github.com/seata/seata/pull/5050)] fix global session is not change to Committed in saga mode
- [[#5052](https://github.com/seata/seata/pull/5052)] fix update join condition placeholder param error
- [[#5031](https://github.com/seata/seata/pull/5031)] fix mysql InsertOnDuplicateUpdate should not use null index value as image sql query condition
- [[#5075](https://github.com/seata/seata/pull/5075)] fix InsertOnDuplicateUpdateExecutor could not intercept the sql which has no primary and unique key
- [[#5093](https://github.com/seata/seata/pull/5093)] fix access key loss after seata server restart
- [[#5092](https://github.com/seata/seata/pull/5092)] fix when seata and jpa are used together, their AutoConfiguration order is incorrect
- [[#5109](https://github.com/seata/seata/pull/5109)] fix NPE caused when there is no @GlobalTransactional annotation on the RM side
- [[#5098](https://github.com/seata/seata/pull/5098)] Druid disable oracle implicit cache
- [[#4860](https://github.com/seata/seata/pull/4860)] fix metrics tags coverage in the seata-server side
- [[#5028](https://github.com/seata/seata/pull/5028)] fix insert value null parsed as string in insert on duplicate SQL
- [[#5078](https://github.com/seata/seata/pull/5078)] fix could not intercept the sql witch has no primary and unique key
- [[#5097](https://github.com/seata/seata/pull/5097)] fix access key loss after server restart
- [[#5131](https://github.com/seata/seata/pull/5131)] fix rollback xa connection active state
- [[#5134](https://github.com/seata/seata/pull/5134)] fix hikari datasource auto proxy fail
- [[#5163](https://github.com/seata/seata/pull/5163)] fix bad service configuration file and compilation failure

### optimize：
- [[#4774](https://github.com/seata/seata/pull/4774)] optimize mysql8 dependencies for seataio/seata-server image
- [[#4790](https://github.com/seata/seata/pull/4790)] Add a github action to publish Seata to OSSRH
- [[#4765](https://github.com/seata/seata/pull/4765)] mysql 8.0.29 not should be hold for connection
- [[#4750](https://github.com/seata/seata/pull/4750)] optimize unBranchLock romove xid
- [[#4797](https://github.com/seata/seata/pull/4797)] optimize the github actions
- [[#4800](https://github.com/seata/seata/pull/4800)] Add NOTICE as Apache License V2
- [[#4681](https://github.com/seata/seata/pull/4681)] optimize the check lock during global transaction
- [[#4761](https://github.com/seata/seata/pull/4761)] use hget replace hmget because only one field
- [[#4414](https://github.com/seata/seata/pull/4414)] exclude log4j dependencies
- [[#4836](https://github.com/seata/seata/pull/4836)] optimize BaseTransactionalExecutor#buildLockKey(TableRecords rowsIncludingPK) method more readable
- [[#4865](https://github.com/seata/seata/pull/4865)] fix some security vulnerabilities in GGEditor
- [[#4590](https://github.com/seata/seata/pull/4590)] auto degrade enable to dynamic configure
- [[#4490](https://github.com/seata/seata/pull/4490)] tccfence log table delete by index
- [[#4911](https://github.com/seata/seata/pull/4911)] add license checker workflow
- [[#4917](https://github.com/seata/seata/pull/4917)] upgrade package-lock.json fix vulnerabilities
- [[#4924](https://github.com/seata/seata/pull/4924)] optimize pom dependencies
- [[#4932](https://github.com/seata/seata/pull/4932)] extract the default values for some properties
- [[#4925](https://github.com/seata/seata/pull/4925)] optimize java doc warning
- [[#4921](https://github.com/seata/seata/pull/4921)] fix some vulnerabilities in console and upgrade skywalking-eyes
- [[#4936](https://github.com/seata/seata/pull/4936)] optimize read of storage configuration
- [[#4946](https://github.com/seata/seata/pull/4946)] pass the sqlexception to client when get lock
- [[#4962](https://github.com/seata/seata/pull/4962)] optimize build and fix the base image
- [[#4974](https://github.com/seata/seata/pull/4974)] optimize cancel the limit on the number of globalStatus queries in Redis mode
- [[#4981](https://github.com/seata/seata/pull/4981)] optimize tcc fence record not exists errMessage
- [[#4985](https://github.com/seata/seata/pull/4985)] fix undo_log id repeat
- [[#4995](https://github.com/seata/seata/pull/4995)] fix mysql InsertOnDuplicateUpdate duplicate pk condition in after image query sql
- [[#5047](https://github.com/seata/seata/pull/5047)] remove useless code
- [[#5051](https://github.com/seata/seata/pull/5051)] undo log dirty throw BranchRollbackFailed_Unretriable
- [[#5075](https://github.com/seata/seata/pull/5075)] intercept the InsertOnDuplicateUpdate statement which has no primary key and unique index value
- [[#5104](https://github.com/seata/seata/pull/5104)] remove the druid dependency in ConnectionProxy
- [[#5124](https://github.com/seata/seata/pull/5124)] support oracle on delete tccfence logs
- [[#4468](https://github.com/seata/seata/pull/4968)] support kryo 5.3.0
- [[#4807](https://github.com/seata/seata/pull/4807)] optimize docker image and oss publish
- [[#4445](https://github.com/seata/seata/pull/4445)] optimize transaction timeout judgment
- [[#4958](https://github.com/seata/seata/pull/4958)] do not execute triggerAfterCommit() if timeout
- [[#4582](https://github.com/seata/seata/pull/4582)] redis mode support sorted set by timeout
- [[#4963](https://github.com/seata/seata/pull/4963)] add ARM64 CI workflow
- [[#4434](https://github.com/seata/seata/pull/4434)] remove seata-server's CMS parameters

### test:
- [[#4411](https://github.com/seata/seata/pull/4411)] add UT for oracle in AT mode
- [[#4794](https://github.com/seata/seata/pull/4794)] try to fix the test `DataSourceProxyTest.getResourceIdTest()`
- [[#5101](https://github.com/seata/seata/pull/5101)] fix ClassNotFoundException during the zk unit test

Thanks to these contributors for their code commits. Please report an unintended omission.

<!-- Please make sure your Github ID is in the list below -->
- [slievrly](https://github.com/slievrly)
- [renliangyu857](https://github.com/renliangyu857)
- [wangliang181230](https://github.com/wangliang181230)
- [a364176773](https://github.com/a364176773)
- [tuwenlin](https://github.com/tuwenlin)
- [lcmvs](https://github.com/lcmvs)
- [AlexStocks](https://github.com/AlexStocks)
- [liujunlin5168](https://github.com/liujunlin5168)
- [pengten](https://github.com/pengten)
- [liuqiufeng](https://github.com/liuqiufeng)
- [yujianfei1986](https://github.com/yujianfei1986)
- [Bughue](https://github.com/Bughue)
- [AlbumenJ](https://github.com/AlbumenJ)
- [doubleDimple](https://github.com/doubleDimple)
- [jsbxyyx](https://github.com/jsbxyyx)
- [tuwenlin](https://github.com/tuwenlin)
- [CrazyLionLi](https://github.com/JavaLionLi)
- [whxxxxx](https://github.com/whxxxxx)
- [neillee95](https://github.com/neillee95)
- [crazy-sheep](https://github.com/crazy-sheep)
- [zhangzq7](https://github.com/zhangzq7)
- [l81893521](https://github.com/l81893521)
- [zhuyoufeng](https://github.com/zhuyoufeng)
- [xingfudeshi](https://github.com/xingfudeshi)
- [odidev](https://github.com/odidev)
- [miaoxueyu](https://github.com/miaoxueyu)


Also, we receive many valuable issues, questions and advices from our community. Thanks for you all.

#### Link

- **Seata:** https://github.com/seata/seata
- **Seata-Samples:** https://github.com/seata/seata-samples
- **Release:** https://github.com/seata/seata/releases
- **WebSite:** https://seata.io


</details>
