<!--
    Licensed to the Apache Software Foundation (ASF) under one or more
    contributor license agreements.  See the NOTICE file distributed with
    this work for additional information regarding copyright ownership.
    The ASF licenses this file to You under the Apache License, Version 2.0
    (the "License"); you may not use this file except in compliance with
    the License.  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0
    
    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
-->
### 1.6.1

[source](https://github.com/seata/seata/archive/v1.6.1.zip) |
[binary](https://github.com/seata/seata/releases/download/v1.6.1/seata-server-1.6.1.zip)

<details>	
  <summary><mark>Release notes</mark></summary>	


### Seata 1.6.1

Seata 1.6.1 Released.

Seata is an easy-to-use, high-performance, open source distributed transaction solution.

The version is updated as follows:

### feature:
- [[#5115](https://github.com/seata/seata/pull/5115)] support for `spring-boot:3.x`

### bugfix:
- [[#5179](https://github.com/seata/seata/pull/5179)] fix ClassNotFoundException when server starts using Eureka

### optimize:
- [[#5120](https://github.com/seata/seata/pull/5120)] unify the format of configuration items in yml files
- [[#5180](https://github.com/seata/seata/pull/5180)] GlobalTransactionScanner,SeataAutoDataSourceProxyCreator declare @bean methods as static
- [[#5182](https://github.com/seata/seata/pull/5182)] fix some security vulnerabilities in GGEditor
- [[#5183](https://github.com/seata/seata/pull/5183)] optimize the default values for some switches

Thanks to these contributors for their code commits. Please report an unintended omission.

<!-- Please make sure your Github ID is in the list below -->
- [slievrly](https://github.com/slievrly)
- [wangliang181230](https://github.com/wangliang181230)
- [xingfudeshi](https://github.com/xingfudeshi)
- [whxxxxx](https://github.com/whxxxxx)
- [xssdpgy](https://github.com/xssdpgy)

Also, we receive many valuable issues, questions and advices from our community. Thanks for you all.

#### Link

- **Seata:** https://github.com/seata/seata
- **Seata-Samples:** https://github.com/seata/seata-samples
- **Release:** https://github.com/seata/seata/releases
- **WebSite:** https://seata.io


</details>
