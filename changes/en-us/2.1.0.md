<!--
    Licensed to the Apache Software Foundation (ASF) under one or more
    contributor license agreements.  See the NOTICE file distributed with
    this work for additional information regarding copyright ownership.
    The ASF licenses this file to You under the Apache License, Version 2.0
    (the "License"); you may not use this file except in compliance with
    the License.  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0
    
    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
-->
Add changes here for all PR submitted to the 2.x branch.

<!-- Please add the `changes` to the following location(feature/bugfix/optimize/test) based on the type of PR -->

### feature:
- [[#6370](https://github.com/seata/seata/pull/6370)] seata saga decouple spring, optimize architecture.
- [[#6205](https://github.com/apache/incubator-seata/pull/6205)] mock server
- [[#6169](https://github.com/apache/incubator-seata/pull/6169)] full support for states in the refactored state machine designer
- [[#6230](https://github.com/apache/incubator-seata/pull/6230)] RocketMQ transaction are supported
- [[#6326](https://github.com/apache/incubator-seata/pull/6326)] support raft node metadata sync
- [[#6415](https://github.com/apache/incubator-seata/pull/6415)] support autolayout in seata-statemachine-designer

### bugfix:
- [[#6090](https://github.com/apache/incubator-seata/pull/6090)] fix the TCC aspect exception handling process, do not wrapping the internal call exceptions
- [[#6075](https://github.com/apache/incubator-seata/pull/6075)] fix missing table alias for on update column of image SQL
- [[#6086](https://github.com/apache/incubator-seata/pull/6086)] fix oracle column alias cannot find
- [[#6085](https://github.com/apache/incubator-seata/pull/6085)] fix jdk9+ compile error
- [[#6101](https://github.com/apache/incubator-seata/pull/6101)] fix the consumer can't generate tcc proxy in dubbo 3.x version
- [[#6077](https://github.com/apache/incubator-seata/pull/6077)] fix could not rollback when table with multiple primary
- [[#6121](https://github.com/apache/incubator-seata/pull/6121)] fix the branch transaction order error when rolling back
- [[#6182](https://github.com/apache/incubator-seata/pull/6182)] fix guava-32.0.0-jre.jar zip file is empty in ci
- [[#6196](https://github.com/apache/incubator-seata/pull/6196)] fix asf config file format error
- [[#6143](https://github.com/apache/incubator-seata/pull/6143)] gracefully shut down the server
- [[#6204](https://github.com/apache/incubator-seata/pull/6204)] fix the problem that The incorrect configuration needs to be fixed
- [[#6248](https://github.com/apache/incubator-seata/pull/6248)] fix JDBC resultSet, statement, connection closing order
- [[#6261](https://github.com/apache/incubator-seata/pull/6261)] AT mode support the URL of a PGSQL cluster
- [[#6256](https://github.com/apache/incubator-seata/pull/6256)] fix raft-discovery cannot read registry configuration for seata-all sdk
- [[#6232](https://github.com/apache/incubator-seata/pull/6232)] convert to utf8mb4 if mysql column is json type
- [[#6278](https://github.com/apache/incubator-seata/pull/6278)] fix ProtocolV1SerializerTest failed
- [[#6324](https://github.com/apache/incubator-seata/pull/6324)] fix Parse protocol file failed
- [[#6331](https://github.com/apache/incubator-seata/pull/6331)] fixed the problem that TCC nested transactions cannot add TwoPhaseBusinessAction and GlobalTransactional annotations at the same time
- [[#6354](https://github.com/apache/incubator-seata/pull/6354)] fix dynamic degradation does not work properly
- [[#6363](https://github.com/apache/incubator-seata/pull/6363)] fix known problems of docker image
- [[#6372](https://github.com/apache/incubator-seata/pull/6372)] fix initializing the sql file postgresql.sql index name conflict
- [[#6380](https://github.com/apache/incubator-seata/pull/6380)] fix sql exception when checking for the existence of the UNDO_LOG table on SQL server
- [[#6385](https://github.com/apache/incubator-seata/pull/6385)] fix the bug where Role.participant does not execute hooks but clears them.
- [[#6465](https://github.com/apache/incubator-seata/pull/6465)] fix(6257): fix saga mode replay context lost start in 2.x
- [[#6469](https://github.com/apache/incubator-seata/pull/6469)] fix Error in insert sql of [lock_table] data table to sqlserver database
- [[#6496](https://github.com/apache/incubator-seata/pull/6496)] fix XA did not rollback but close when executing a long-running SQL(or deadlock SQL)
- [[#6493](https://github.com/apache/incubator-seata/pull/6493)] fix SQLServer-related SQL error in seata server when using database of SQLServer
- [[#6497](https://github.com/apache/incubator-seata/pull/6497)] fix tcc properties class when autoconfigure
- [[#6554](https://github.com/apache/incubator-seata/pull/6554)] fix unfixed serializer
- [[#6555](https://github.com/apache/incubator-seata/pull/6555)] businessActionContext is compatible with io seata
- [[#6553](https://github.com/apache/incubator-seata/pull/6553)] fix saga "cannot matching status"
- [[#6575](https://github.com/apache/incubator-seata/pull/6575)] fix io.seata ActionInterceptorHandler use org.apache.seata BusinessActionContextParameter


### optimize:
- [[#6031](https://github.com/apache/incubator-seata/pull/6031)] add a check for the existence of the undolog table
- [[#6089](https://github.com/apache/incubator-seata/pull/6089)] modify the semantics of RaftServerFactory and remove unnecessary singleton
- [[#4473](https://github.com/apache/incubator-seata/pull/4473)] rm appdata size limit
- [[#6071](https://github.com/apache/incubator-seata/pull/6071)] add git infos to jars
- [[#6042](https://github.com/apache/incubator-seata/pull/6042)] add secure authentication to interfaces in ClusterController
- [[#6091](https://github.com/apache/incubator-seata/pull/6091)] Optimizing the method of obtaining the tc address during raft authentication
- [[#6098](https://github.com/apache/incubator-seata/pull/6098)] optimize the retry logic in the acquireMetadata method
- [[#6034](https://github.com/apache/incubator-seata/pull/6034)] using namespace from command line when deployment with helm charts
- [[#6116](https://github.com/apache/incubator-seata/pull/6034)] remove lgtm.com stuff
- [[#6148](https://github.com/apache/incubator-seata/pull/6148)] support Nacos ram role authentication
- [[#6145](https://github.com/apache/incubator-seata/pull/6145)] upgrade jettison to 1.5.4
- [[#6164](https://github.com/apache/incubator-seata/pull/6164)] redis registry push empty protection optimize
- [[#6174](https://github.com/apache/incubator-seata/pull/6174)] add ASF basic config
- [[#6181](https://github.com/apache/incubator-seata/pull/6181)] update contributing doc
- [[#6179](https://github.com/apache/incubator-seata/pull/6179)] remove <AUTHOR>
- [[#6176](https://github.com/apache/incubator-seata/pull/6176)] update source header
- [[#6178](https://github.com/apache/incubator-seata/pull/6178)] update the header of Apache License
- [[#6186](https://github.com/apache/incubator-seata/pull/6186)] update README.md(update mailing list and repository urls)
- [[#6184](https://github.com/apache/incubator-seata/pull/6184)] update NOTICE file
- [[#6192](https://github.com/apache/incubator-seata/pull/6192)] remove the useless file
- [[#6194](https://github.com/apache/incubator-seata/pull/6194)] fix asf.yaml parse error
- [[#5399](https://github.com/apache/incubator-seata/pull/5399)] optimizing branch register resource only at RM server end
- [[#6154](https://github.com/apache/incubator-seata/pull/6154)] console log optimize for "kubectl logs -f"
- [[#6116](https://github.com/apache/incubator-seata/pull/6116)] rewrite NettyPoolKey's hashcode and equals to fix duplicate construction of channel object pools
- [[#6195](https://github.com/apache/incubator-seata/pull/6195)] update the url in change log to apache/incubator-seata
- [[#6200](https://github.com/apache/incubator-seata/pull/6200)] cancel required_status_checks
- [[#6201](https://github.com/apache/incubator-seata/pull/6201)] restore required_status_checks kept to remove context validation
- [[#6218](https://github.com/apache/incubator-seata/pull/6218)] remove Seata-Docker link
- [[#6227](https://github.com/apache/incubator-seata/pull/6227)] validate that the primary key is free of illegal characters
- [[#6004](https://github.com/apache/incubator-seata/pull/6004)] optimize RM TM startup connect server fail fast
- [[#6243](https://github.com/apache/incubator-seata/pull/6243)] optimize links in the console header
- [[#6238](https://github.com/apache/incubator-seata/pull/6238)] optimize some files
- [[#6239](https://github.com/apache/incubator-seata/pull/6239)] update security policy, disclaimer and notice
- [[#6245](https://github.com/apache/incubator-seata/pull/6245)] in file mode, the configuration in the application takes effect, when the spring configuration in the configuration center is changed
- [[#6247](https://github.com/apache/incubator-seata/pull/6247)] optimize asf.yml
- [[#6259](https://github.com/apache/incubator-seata/pull/6259)] modify error message which is global session size more than config
- [[#6264](https://github.com/apache/incubator-seata/pull/6264)] fix jib-maven-plugin build failed
- [[#6246](https://github.com/apache/incubator-seata/pull/6246)] build the frontend at the same time as the maven build
- [[#6268](https://github.com/apache/incubator-seata/pull/6268)] optimize outdate npmjs dependencies in console
- [[#6271](https://github.com/apache/incubator-seata/pull/6271)] unifty the git information
- [[#6265](https://github.com/apache/incubator-seata/pull/6265)] optimization fails to build frontend on arm64
- [[#6267](https://github.com/apache/incubator-seata/pull/6267)] add Server deserialization validation
- [[#6275](https://github.com/apache/incubator-seata/pull/6275)] optimize the label's format in .asf.yaml
- [[#6291](https://github.com/apache/incubator-seata/pull/6291)] seata-server is developed in idea and console support output logs
- [[#6283](https://github.com/apache/incubator-seata/pull/6283)] add a compatible module to support io.seata APIs
- [[#6294](https://github.com/apache/incubator-seata/pull/6294)] split the frontend resource build process into separate profiles
- [[#6285](https://github.com/apache/incubator-seata/pull/6285)] optimize time query conditions in the console
- [[#6297](https://github.com/apache/incubator-seata/pull/6297)] fix problem of `maven-pmd-plugin`
- [[#6298](https://github.com/apache/incubator-seata/pull/6298)] repackage name to org.apache.seata
- [[#6302](https://github.com/apache/incubator-seata/pull/6302)] add io.seata package shade
- [[#6306](https://github.com/apache/incubator-seata/pull/6306)] replace some URL to org/apache/seata
- [[#6304](https://github.com/apache/incubator-seata/pull/6304)] disable Publish OSSRH workflow
- [[#6310](https://github.com/apache/incubator-seata/pull/6310)] seata-server compatible io.seata package
- [[#6301](https://github.com/apache/incubator-seata/pull/6301)] upgrade console frontend dependencies and supported nodejs versions
- [[#6301](https://github.com/apache/incubator-seata/pull/6312)] add saga related io.seata compatible api
- [[#6313](https://github.com/apache/incubator-seata/pull/6313)] console display the version number
- [[#6315](https://github.com/apache/incubator-seata/pull/6315)] compatible with lower versions of SPI
- [[#6327](https://github.com/apache/incubator-seata/pull/6327)] compatible with integration.http and integration.http.Jakarta
- [[#6328](https://github.com/apache/incubator-seata/pull/6328)] compatible with integration.grpc
- [[#6330](https://github.com/apache/incubator-seata/pull/6330)] remove mariadb API
- [[#6329](https://github.com/apache/incubator-seata/pull/6312)] add saga subcomponent-level io.seata compatible api
- [[#6254](https://github.com/apache/incubator-seata/pull/6254)] optimize Hessian Serialize
- [[#6332](https://github.com/apache/incubator-seata/pull/6332)] remove mysql dependency from the distribution package
- [[#6343](https://github.com/apache/incubator-seata/pull/6343)] compatible with tm module and rm-datasource module
- [[#6357](https://github.com/apache/incubator-seata/pull/6357)] optimize serialization/deserialization of protocol codec
- [[#6345](https://github.com/apache/incubator-seata/pull/6345)] compatible with tcc module
- [[#6356](https://github.com/apache/incubator-seata/pull/6356)] remove authentication from the health check page
- [[#6360](https://github.com/apache/incubator-seata/pull/6360)] optimize 401 issues for some links
- [[#6366](https://github.com/apache/incubator-seata/pull/6366)] optimized globaltransaction compatibility issues
- [[#6369](https://github.com/apache/incubator-seata/pull/6369)] optimize arm64 ci
- [[#6386](https://github.com/apache/incubator-seata/pull/6386)] replace `byte-buddy` to JDK proxy
  in `ConfigurationCache`
- [[#6391](https://github.com/apache/incubator-seata/pull/6091)] forbid duplicate registration of TCC resources
- [[#6393](https://github.com/apache/incubator-seata/pull/6393)] determine the version before sync metadata and add
  retry mechanism
- [[#6387](https://github.com/apache/incubator-seata/pull/6387)] optimize tcc use compatible
- [[#6402](https://github.com/apache/incubator-seata/pull/6402)] optimize rm-datasource use compatible
- [[#6403](https://github.com/apache/incubator-seata/pull/6403)] optimize config compatible module
- [[#6419](https://github.com/apache/incubator-seata/pull/6419)] optimize integration-tx-api compatible
- [[#6427](https://github.com/apache/incubator-seata/pull/6427)] support spi、saga、spring module compatible
- [[#6442](https://github.com/apache/incubator-seata/pull/6442)] clarify if conditions
- [[#6487](https://github.com/apache/incubator-seata/pull/6487)] fix typo and package name
- [[#6442](https://github.com/apache/incubator-seata/pull/6442)] clarify if conditions
- [[#6405](https://github.com/apache/incubator-seata/pull/6405)] fix kotlin compile failure
- [[#6412](https://github.com/apache/incubator-seata/pull/6412)] optimize core compatible module
- [[#6429](https://github.com/apache/incubator-seata/pull/6429)] remove repetitive words
- [[#6518](https://github.com/apache/incubator-seata/pull/6518)] optimize ConfigurationCache proxy method
- [[#6458](https://github.com/apache/incubator-seata/pull/6458)] add null value check for MAC address
- [[#6516](https://github.com/apache/incubator-seata/pull/6516)] optimize code format
- [[#6529](https://github.com/apache/incubator-seata/pull/6529)] optimize release maven plugin
- [[#6548](https://github.com/apache/incubator-seata/pull/6548)] upgrade the byte-buddy version to 1.14.15
- [[#6539](https://github.com/apache/incubator-seata/pull/6539)] add subcomponents license
- [[#6540](https://github.com/apache/incubator-seata/pull/6540)] exclude com.google.guava:listenablefuture
- [[#6549](https://github.com/apache/incubator-seata/pull/6549)] macos workflow support arm testing
- [[#6558](https://github.com/apache/incubator-seata/pull/6558)] remove mysql-connector-java from pom.xml
- [[#6570](https://github.com/apache/incubator-seata/pull/6570)] add notice file to binary
- [[#6578](https://github.com/apache/incubator-seata/pull/6578)] registry.conf supplemented raft configuration
- [[#6576](https://github.com/apache/incubator-seata/pull/6576)] remove oracle datatype parser
- [[#6583](https://github.com/apache/incubator-seata/pull/6583)] optimize the default compilation to be independent of the Git Env
- [[#6585](https://github.com/apache/incubator-seata/pull/6585)] optimize compatible module pom.xml
- [[#6597](https://github.com/apache/incubator-seata/pull/6597)] remove binary from source code
- [[#6605](https://github.com/apache/incubator-seata/pull/6605)] revised the license and notice
- [[#6609](https://github.com/apache/incubator-seata/pull/6609)] revised the notice file
- [[#6610](https://github.com/apache/incubator-seata/pull/6610)] revised the notice file

### security:
- [[#6069](https://github.com/apache/incubator-seata/pull/6069)] Upgrade Guava dependencies to fix security vulnerabilities
- [[#6145](https://github.com/apache/incubator-seata/pull/6145)] upgrade jettison to 1.5.4
- [[#6144](https://github.com/apache/incubator-seata/pull/6144)] upgrade nacos client to 1.4.6
- [[#6147](https://github.com/apache/incubator-seata/pull/6147)] upgrade kafka-clients to 3.6.1
- [[#6339](https://github.com/apache/incubator-seata/pull/6339)] upgrade spring mvc and tomcat.embed
- [[#6340](https://github.com/apache/incubator-seata/pull/6340)] upgrade and tidy some dependencies
- [[#6350](https://github.com/apache/incubator-seata/pull/6350)] remove enableDegrade properties
- [[#6349](https://github.com/apache/incubator-seata/pull/6349)] transfer dockerhub repo
- [[#6362](https://github.com/apache/incubator-seata/pull/6362)] upgrade Spring related dependence
- [[#6375](https://github.com/apache/incubator-seata/pull/6375)] override console nested dependencies

### test:
- [[#6081](https://github.com/apache/incubator-seata/pull/6081)] add `test-os.yml` for testing the OS
- [[#6125](https://github.com/apache/incubator-seata/pull/6125)] unbind xid in TransactionTemplateTest
- [[#6157](https://github.com/apache/incubator-seata/pull/6157)] increase common module unit test coverage
- [[#6250](https://github.com/apache/incubator-seata/pull/6250)] increase seata-core module unit test coverage
- [[#6325](https://github.com/apache/incubator-seata/pull/6325)] fix mockServerTest fail cause using same port with seata-server
- [[#6430](https://github.com/apache/incubator-seata/pull/6430)] increase common module unit test coverage
- [[#6456](https://github.com/apache/incubator-seata/pull/6456)] adjust the test cases related to dynamic configuration
- [[#6466](https://github.com/apache/incubator-seata/pull/6466)] support redis integration testing
- [[#6484](https://github.com/apache/incubator-seata/pull/6484)] fix FileConfigurationTest and MockServerTest fail
- [[#6545](https://github.com/apache/incubator-seata/pull/6545)] fix TestConfigCustomSPI compatibility test fail
- [[#6560](https://github.com/apache/incubator-seata/pull/6560)] fix mock-server test, do not shutdown in Runtime.getRuntime().addShutdownHook
- [[#6565](https://github.com/apache/incubator-seata/pull/6565)] fix testCompensationStateMachine fail

### refactor:
- [[#6280](https://github.com/apache/incubator-seata/pull/6280)] refactor Saga designer using diagram-js
- [[#6269](https://github.com/apache/incubator-seata/pull/6269)] standardize Seata Exception
- [[#6420](https://github.com/apache/incubator-seata/pull/6420)] refactor Configuration Cache

Thanks to these contributors for their code commits. Please report an unintended omission.

<!-- Please make sure your Github ID is in the list below -->
- [slievrly](https://github.com/slievrly)
- [ptyin](https://github.com/ptyin)
- [laywin](https://github.com/laywin)
- [imcmai](https://github.com/imcmai)
- [DroidEye2ONGU](https://github.com/DroidEye2ONGU)
- [funky-eyes](https://github.com/funky-eyes)
- [Bughue](https://github.com/Bughue)
- [wangliang181230](https://github.com/wangliang181230)
- [ggbocoder](https://github.com/ggbocoder)
- [leezongjie](https://github.com/leezongjie)
- [l81893521](https://github.com/l81893521)
- [baiyangtx](https://github.com/baiyangtx)
- [lightClouds917](https://github.com/lightClouds917)
- [xingfudeshi](https://github.com/xingfudeshi)
- [PleaseGiveMeTheCoke](https://github.com/PleaseGiveMeTheCoke)
- [sunrui1225](https://github.com/sunrui1225)
- [PeppaO](https://github.com/PeppaO)
- [AlbumenJ](https://github.com/AlbumenJ)
- [dreamskyvision](https://github.com/dreamskyvision)
- [jsbxyyx](https://github.com/jsbxyyx)
- [liuqiufeng](https://github.com/liuqiufeng)
- [saberyjs](https://github.com/SABERYJS)
- [gggyd123](https://github.com/gggyd123)
- [jonasHanhan](https://github.com/jonasHanhan)
- [Code-breaker1998](https://github.com/Code-breaker1998)
- [yixia](https://github.com/wt-better)
- [MikhailNavitski](https://github.com/MikhailNavitski)
- [deung](https://github.com/deung)
- [tanyaofei](https://github.com/tanyaofei)
- [xjlgod](https://github.com/xjlgod)
- [TakeActionNow2019](https://github.com/TakeActionNow2019)
- [sunxunle](https://github.com/sunxunle)
- [bageyang](https://github.com/bageyang)
- [YeonCheolGit](https://github.com/YeonCheolGit)

Also, we receive many valuable issues, questions and advices from our community. Thanks for you all.
