<!--
    Licensed to the Apache Software Foundation (ASF) under one or more
    contributor license agreements.  See the NOTICE file distributed with
    this work for additional information regarding copyright ownership.
    The ASF licenses this file to You under the Apache License, Version 2.0
    (the "License"); you may not use this file except in compliance with
    the License.  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0
    
    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
-->
### 1.4.2  	

 [source](https://github.com/seata/seata/archive/v1.4.2.zip) |	
 [binary](https://github.com/seata/seata/releases/download/v1.4.2/seata-server-1.4.2.zip) 	

<details>	
  <summary><mark>Release notes</mark></summary>	


  ### Seata 1.4.2 	

  Seata 1.4.2 Released.	

  Seata is an easy-to-use, high-performance, open source distributed transaction solution.	

  The version is updated as follows:	

  ### feature：	

  - [[#3172](https://github.com/seata/seata/pull/3172)] support undo_loge compression mode in AT	
  - [[#3372](https://github.com/seata/seata/pull/3372)] Saga support customize whether update last retry log	
  - [[#3411](https://github.com/seata/seata/pull/3411)] support seata server thread pool parameters configuration	
  - [[#3348](https://github.com/seata/seata/pull/3348)] support redis sentinel storage mode in TC	
  - [[#2667](https://github.com/seata/seata/pull/2667)] support password decryption	when using db and redis storage mode
  - [[#3427](https://github.com/seata/seata/pull/3427)] add distributed lock interface	
  - [[#3443](https://github.com/seata/seata/pull/3443)] send the `seata-server` log to `logstash` or `kafka`
  - [[#3486](https://github.com/seata/seata/pull/3486)] add transaction service group for metric
  - [[#3317](https://github.com/seata/seata/pull/3317)] support to obtain multiple configurations through a single node when using zookeeper as configuration center 
  - [[#2933](https://github.com/seata/seata/pull/2933)] add antlr for mysql sqlparser
  - [[#3228](https://github.com/seata/seata/pull/3228)] support custom serialization plugin
  - [[#3516](https://github.com/seata/seata/pull/3516)] support acl-token when consul is used registry and configuration center 
  - [[#3116](https://github.com/seata/seata/pull/3116)] support configuring apolloService and apolloCluster
  - [[#3468](https://github.com/seata/seata/pull/3468)] saga support loop execution on state
  - [[#3447](https://github.com/seata/seata/pull/3447)] support Transaction context printing in logging framework


  ### bugfix：	

  - [[#3258](https://github.com/seata/seata/pull/3258)] fix AsyncWorker potential OOM problem 	
  - [[#3293](https://github.com/seata/seata/pull/3293)] fix configuration cache get value type mismatch exception
  - [[#3241](https://github.com/seata/seata/pull/3241)] forbidden use order by or limit in multi sql	
  - [[#3406](https://github.com/seata/seata/pull/3406)] fix the value can not be push to nacos when special charset in config.txt	
  - [[#3418](https://github.com/seata/seata/pull/3418)] fix getGeneratedKeys may get history pk	
  - [[#3408](https://github.com/seata/seata/pull/3408)] fix the NPE problem of jar running mode when the third-dependency on separate packaging
  - [[#3431](https://github.com/seata/seata/pull/3431)] fix property bean may not be initialized when reading configuration	
  - [[#3413](https://github.com/seata/seata/pull/3413)] fix the logic of rollback to savepoint and release to savepoint	
  - [[#3367](https://github.com/seata/seata/pull/3367)] when the xa branch is rollback, it cannot be executed due to idle state
  - [[#3448](https://github.com/seata/seata/pull/3448)] reduce unnecessary competition and remove missing locks 
  - [[#3451](https://github.com/seata/seata/pull/3451)] fix set auto-commit to true when local transactions are not being used. Failure to compete for a lock causes the global transaction to exit, invaliding the global row lock and dirty writing of the data.
  - [[#3481](https://github.com/seata/seata/pull/3481)] fix seata node refresh failure because of consul client throws exceptions
  - [[#3491](https://github.com/seata/seata/pull/3491)] fix typo in README.md
  - [[#3531](https://github.com/seata/seata/pull/3531)] fix the NPE of RedisTransactionStoreManager when get branch transactions
  - [[#3500](https://github.com/seata/seata/pull/3500)] fix oracle and postgreSQL can't query column info
  - [[#3560](https://github.com/seata/seata/pull/3560)] fix the problem that the asynchronous task of the transactions in the committing state has no time threshold and cannot recover the transaction
  - [[#3555](https://github.com/seata/seata/pull/3555)] do not call setBlob to invalid the jdbc exception
  - [[#3540](https://github.com/seata/seata/pull/3540)] fix server distribution missing files
  - [[#3597](https://github.com/seata/seata/pull/3597)] fix the possible NPE
  - [[#3568](https://github.com/seata/seata/pull/3568)] fix automatic datasource agent caused by ConcurrentHashMap.computeIfAbsent Deadlock problem 
  - [[#3402](https://github.com/seata/seata/pull/3402)] fix the problem that the updated column cannot be resolved because the field name in the updated SQL contains the database name
  - [[#3464](https://github.com/seata/seata/pull/3464)] fix test case NPE and StackTraceLogger's log.
  - [[#3522](https://github.com/seata/seata/pull/3522)] fix register branch and store undolog when AT branch does not need compete lock
  - [[#3635](https://github.com/seata/seata/pull/3635)] fix pushing notification failed when the configuration changed in zookeeper
  - [[#3133](https://github.com/seata/seata/pull/3133)] fix the case that could not retry acquire global lock
  - [[#3156](https://github.com/seata/seata/pull/3156)] optimize the logic of SpringProxyUtils.findTargetClass


  ### optimize： 	

  - [[#3341](https://github.com/seata/seata/pull/3341)] optimize the format of the path to the specified configuration file
  - [[#3385](https://github.com/seata/seata/pull/3385)] optimize github action and fix unit test failure	
  - [[#3175](https://github.com/seata/seata/pull/3175)] improve UUIDGenerator using "history time" version of snowflake algorithm 	
  - [[#3291](https://github.com/seata/seata/pull/3291)] mysql jdbc connect param	
  - [[#3336](https://github.com/seata/seata/pull/3336)] support using System.getProperty to get netty config property
  - [[#3369](https://github.com/seata/seata/pull/3369)] add github action secrets env for dockerHub	
  - [[#3343](https://github.com/seata/seata/pull/3343)] Migrate CI provider from Travis CI to Github Actions	
  - [[#3397](https://github.com/seata/seata/pull/3397)] add the change records folder	
  - [[#3303](https://github.com/seata/seata/pull/3303)] supports reading all configurations from a single Nacos dataId	
  - [[#3380](https://github.com/seata/seata/pull/3380)] globalTransactionScanner listener optimize	
  - [[#3123](https://github.com/seata/seata/pull/3123)] optimize the packing strategy of seata-server	
  - [[#3415](https://github.com/seata/seata/pull/3415)] optimize maven clean plugin to clear the distribution directory 	
  - [[#3316](https://github.com/seata/seata/pull/3316)] optimize the property bean may not be initialized while reading config value	
  - [[#3420](https://github.com/seata/seata/pull/3420)] optimize enumerated classes and add unit tests	
  - [[#3533](https://github.com/seata/seata/pull/3533)] added interface to get current transaction role
  - [[#3436](https://github.com/seata/seata/pull/3436)] optimize typo in SQLType class 	
  - [[#3439](https://github.com/seata/seata/pull/3439)] adjust the order of springApplicationContextProvider so that it can be called before the XML bean
  - [[#3248](https://github.com/seata/seata/pull/3248)] optimize the config of load-balance migration to belong the client node
  - [[#3441](https://github.com/seata/seata/pull/3441)] optimize the auto-configuration processing of starter
  - [[#3466](https://github.com/seata/seata/pull/3466)] String comparison uses equalsIgnoreCase()
  - [[#3476](https://github.com/seata/seata/pull/3476)] support when the server parameter passed is hostname, it will be automatically converted to IP
  - [[#3236](https://github.com/seata/seata/pull/3236)] optimize the conditions for executing unlocking
  - [[#3485](https://github.com/seata/seata/pull/3485)] optimize useless codes in ConfigurationFactory
  - [[#3505](https://github.com/seata/seata/pull/3505)] optimize useless if judgments in the GlobalTransactionScanner class
  - [[#3544](https://github.com/seata/seata/pull/3544)] optimize the get pks by auto when auto generated keys is false
  - [[#3549](https://github.com/seata/seata/pull/3549)] unified the length of xid in different tables when using DB storage mode
  - [[#3551](https://github.com/seata/seata/pull/3551)] make RETRY_DEAD_THRESHOLD bigger and configurable
  - [[#3589](https://github.com/seata/seata/pull/3589)] Changed exception check by JUnit API usage
  - [[#3601](https://github.com/seata/seata/pull/3601)] make `LoadBalanceProperties` compatible with `spring-boot:2.x` and above
  - [[#3513](https://github.com/seata/seata/pull/3513)] Saga SpringBeanService invoker support switch json parser
  - [[#3318](https://github.com/seata/seata/pull/3318)] make CLIENT_TABLE_META_CHECKER_INTERVAL configurable
  - [[#3371](https://github.com/seata/seata/pull/3371)] add applicationId for metric
  - [[#3459](https://github.com/seata/seata/pull/3459)] remove duplicate validAddress code
  - [[#3215](https://github.com/seata/seata/pull/3215)] opt the reload during startup in file mode
  - [[#3631](https://github.com/seata/seata/pull/3631)] optimize  nacos-config.py  parameter
  - [[#3638](https://github.com/seata/seata/pull/3638)] optimize the error when use update or delete with join in sql
  - [[#3523](https://github.com/seata/seata/pull/3523)] optimize release savepoint when use oracle
  - [[#3458](https://github.com/seata/seata/pull/3458)] reversion the deleted md
  - [[#3574](https://github.com/seata/seata/pull/3574)] repair Spelling errors in comments in EventBus.java files
  - [[#3573](https://github.com/seata/seata/pull/3573)] fix designer directory path in README.md
  - [[#3662](https://github.com/seata/seata/pull/3662)] update gpg key
  - [[#3664](https://github.com/seata/seata/pull/3664)] optimize some javadocs
  - [[#3637](https://github.com/seata/seata/pull/3637)] register the participating companies and  pull request information

  ### test	

  - [[#3381](https://github.com/seata/seata/pull/3381)] test case for tmClient	
  - [[#3607](https://github.com/seata/seata/pull/3607)] fixed bugs in EventBus unit tests
  - [[#3579](https://github.com/seata/seata/pull/3579)] add test case for StringFormatUtils
  - [[#3365](https://github.com/seata/seata/pull/3365)] optimize ParameterParserTest test case failed	
  - [[#3359](https://github.com/seata/seata/pull/3359)] remove unused test case	
  - [[#3578](https://github.com/seata/seata/pull/3578)] fix UnfinishedStubbing Exception in unit test case
  - [[#3383](https://github.com/seata/seata/pull/3383)] optimize StatementProxyTest unit test



  Thanks to these contributors for their code commits. Please report an unintended omission.  	

  - [slievrly](https://github.com/slievrly) 
  - [caohdgege](https://github.com/caohdgege) 
  - [a364176773](https://github.com/a364176773) 
  - [wangliang181230](https://github.com/wangliang181230)
  - [xingfudeshi](https://github.com/xingfudeshi)
  - [jsbxyyx](https://github.com/jsbxyyx) 
  - [selfishlover](https://github.com/selfishlover)
  - [l8189352](https://github.com/l81893521)
  - [Rubbernecker](https://github.com/Rubbernecker)
  - [lj2018110133](https://github.com/lj2018110133)
  - [github-ganyu](https://github.com/github-ganyu)
  - [dmego](https://github.com/dmego)
  - [spilledyear](https://github.com/spilledyear)
  - [hoverruan](https://github.com/hoverruan ) 
  - [anselleeyy](https://github.com/anselleeyy)
  - [Ifdevil](https://github.com/Ifdevil)
  - [lvxianzheng](https://github.com/lvxianzheng)
  - [MentosL](https://github.com/MentosL)
  - [lian88jian](https://github.com/lian88jian)
  - [litianyu1992](https://github.com/litianyu1992)
  - [xyz327](https://github.com/xyz327)
  - [13414850431](https://github.com/13414850431)
  - [xuande](https://github.com/xuande)
  - [tanggen](https://github.com/tanggen)
  - [eas5](https://github.com/eas5)
  - [nature80](https://github.com/nature80)
  - [ls9527](https://github.com/ls9527)
  - [drgnchan](https://github.com/drgnchan)
  - [imyangyong](https://github.com/imyangyong)
  - [sunlggggg](https://github.com/sunlggggg)
  - [long187](https://github.com/long187)
  - [h-zhi](https://github.com/h-zhi)
  - [StellaiYang](https://github.com/StellaiYang)
  - [slinpq](https://github.com/slinpq)
  - [sustly](https://github.com/sustly)
  - [cznc](https://github.com/cznc)
  - [squallliu](https://github.com/squallliu)
  - [81519434](https://github.com/81519434)
  - [luoxn28](https://github.com/luoxn28)
  
  Also, we receive many valuable issues, questions and advices from our community. Thanks for you all.	

   #### Link	

   - **Seata:** https://github.com/seata/seata  	
   - **Seata-Samples:** https://github.com/seata/seata-samples   	
   - **Release:** https://github.com/seata/seata/releases	
   - **WebSite:** https://seata.io	


</details>
