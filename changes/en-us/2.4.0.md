<!--
    Licensed to the Apache Software Foundation (ASF) under one or more
    contributor license agreements.  See the NOTICE file distributed with
    this work for additional information regarding copyright ownership.
    The ASF licenses this file to You under the Apache License, Version 2.0
    (the "License"); you may not use this file except in compliance with
    the License.  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0
    
    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
-->
### 2.4.0

<details>
  <summary><mark>Release notes</mark></summary>	

### Apache Seata(incubating) 2.4.0

Seata 2.4.0 Released.

Seata is an easy-to-use, high-performance, open source distributed transaction solution.

The version is updated as follows:

### feature:

- [[#7157](https://github.com/apache/incubator-seata/pull/7157)] migrate the console to the naming server
- [[#7213](https://github.com/apache/incubator-seata/pull/7213)] support kingbase xa mode


### bugfix:

- [[#7104](https://github.com/apache/incubator-seata/pull/7104)] fix impl of supportsSourceType is not defined
- [[#7116](https://github.com/apache/incubator-seata/pull/7116)] fix prefix: seata.server.raft.ssl should not be null
- [[#7112](https://github.com/apache/incubator-seata/pull/7112)] bugfix: remove the condition that IPv6 must start with fe80
- [[#7107](https://github.com/apache/incubator-seata/pull/7107)] fix the issue of failing to parse annotations in TCC mode when the business object is a proxy object.
- [[#7124](https://github.com/apache/incubator-seata/pull/7124)] bugfix: GlobalTransactionScanner.afterPropertiesSet need do scanner check
- [[#7135](https://github.com/apache/incubator-seata/pull/7135)] treating a unique index conflict during rollback as a dirty write
- [[#7150](https://github.com/apache/incubator-seata/pull/7150)] The time difference between the raft node and the follower node cannot synchronize data
- [[#7102](https://github.com/apache/incubator-seata/pull/7150)] bugfix: modify XA mode pre commit transaction from commit phase to before close phase
- [[#7188](https://github.com/apache/incubator-seata/pull/7188)] bugfix: Fix missing branchType in BusinessActionContext
- [[#7219](https://github.com/apache/incubator-seata/pull/7219)] bugfix: NotSupportExc can't be thrown out in some cases
- [[#7241](https://github.com/apache/incubator-seata/pull/7241)] upgrade tomcat-embed-core to 9.0.99 to fix CVE-2025-24813
- [[#7272](https://github.com/apache/incubator-seata/pull/7272)] fix: fix transaction info not display
- [[#7277](https://github.com/apache/incubator-seata/pull/7277)] Fix MySQL jdbc driver can't be found properly

### optimize:

- [[#6828](https://github.com/apache/incubator-seata/pull/6828)] spring boot compatible with file.conf and registry.conf
- [[#7012](https://github.com/apache/incubator-seata/pull/7012)] When the number of primary keys exceeds 1000, use union to concatenate the SQL
- [[#7075](https://github.com/apache/incubator-seata/pull/7075)] fast fail when channel is null
- [[#7089](https://github.com/apache/incubator-seata/pull/7089)] support instance registration to the registry center
- [[#7093](https://github.com/apache/incubator-seata/pull/7093)] add a test workflow for JDK 21
- [[#7088](https://github.com/apache/incubator-seata/pull/7088)] expand english abbreviations to full words
- [[#7064](https://github.com/apache/incubator-seata/pull/7064)] remove unnecessary null checks
- [[#7130](https://github.com/apache/incubator-seata/pull/7130)] Expose some keepalive-related configuration for Druid, Hikari, and DBCP
- [[#7131](https://github.com/apache/incubator-seata/pull/7131)] Remove org.codehaus.jackson dependency
- [[#7134](https://github.com/apache/incubator-seata/pull/7134)] upgrade tomcat-embed to 9.0.98
- [[#7138](https://github.com/apache/incubator-seata/pull/7138)] Remove org.eclipse.jetty dependency
- [[#7139](https://github.com/apache/incubator-seata/pull/7139)] upgrade xstream to 1.4.21
- [[#7141](https://github.com/apache/incubator-seata/pull/7141)] remove unused dependencies
- [[#7142](https://github.com/apache/incubator-seata/pull/7142)] upgrade commons-compress to 1.27.1
- [[#7149](https://github.com/apache/incubator-seata/pull/7149)] Fix abnormal character display issues in ./distribution/NOTICE.md
- [[#7170](https://github.com/apache/incubator-seata/pull/7170)] Optimize seata client I/O processing by adjusting thread count
- [[#7187](https://github.com/apache/incubator-seata/pull/7187)] Add dependency-check-maven plugin to detect potential vulnerabilities
- [[#7179](https://github.com/apache/incubator-seata/pull/7179)] Use shared EventLoop for TM and RM clients to reduce thread overhead and improve performance
- [[#7194](https://github.com/apache/incubator-seata/pull/7194)] automatically skipping proxy for datasource of type AbstractRoutingDataSource
- [[#7215](https://github.com/apache/incubator-seata/pull/7215)] intercept non-leader write requests of the console trx operation
- [[#7224](https://github.com/apache/incubator-seata/pull/7224)] optimize changeGlobalStatus interface of console
- [[#7222](https://github.com/apache/incubator-seata/pull/7222)] in raft mode add the vgroup field to global lock
- [[#7229](https://github.com/apache/incubator-seata/pull/7229)] update Notice
- [[#7234](https://github.com/apache/incubator-seata/pull/7234)] discover the raft leader node from the naming server
- [[#7242](https://github.com/apache/incubator-seata/pull/7242)] optimize: optimize ratelimit bucketTokenNumPerSecond config
- [[#7259](https://github.com/apache/incubator-seata/pull/7259)] transfer the logback appender configuration to the yml configuration
- [[#6998](https://github.com/apache/incubator-seata/pull/6998)] skip sending some request if client-version is v0
- [[#7250](https://github.com/apache/incubator-seata/pull/7250)] compatible for client_protocol_version > server_protocol_version
- [[#7242](https://github.com/apache/incubator-seata/pull/7242)] optimize ratelimit bucketTokenNumPerSecond config
- [[#7232](https://github.com/apache/incubator-seata/pull/7232)] add license header
- [[#7260](https://github.com/apache/incubator-seata/pull/7260)] upgrade npmjs dependencies
- [[#7284](https://github.com/apache/incubator-seata/pull/7284)] add dependency-check profile
- [[#7073](https://github.com/apache/incubator-seata/pull/7073)] support virtual thread,replace the usages of synchronized with ReentrantLock
- [[#6756](https://github.com/apache/incubator-seata/pull/6756)] feature: add single server rate limit
- [[#7037](https://github.com/apache/incubator-seata/pull/7037)] support fury undolog parser
- [[#7069](https://github.com/apache/incubator-seata/pull/7069)] Raft cluster mode supports address translation
- [[#7038](https://github.com/apache/incubator-seata/pull/7038)] support fury serializer
- [[#7171](https://github.com/apache/incubator-seata/pull/7171)] support EpollEventLoopGroup in client
- [[#7223](https://github.com/apache/incubator-seata/pull/7223)] apply Spotless with Palantir java format
- [[#7114](https://github.com/apache/incubator-seata/pull/7114)] support raft mode registry to namingserver
- [[#7133](https://github.com/apache/incubator-seata/pull/7133)] Implement scheduled handling for end status transaction
- [[#7183](https://github.com/apache/incubator-seata/pull/7183)] client discovers raft nodes through the naming server
- [[#7182](https://github.com/apache/incubator-seata/pull/7182)] use the ip of the peerId as the host of the raft node
- [[#7181](https://github.com/apache/incubator-seata/pull/7181)] raft implements domain name resolution and selects peerId
- [[#7283](https://github.com/apache/incubator-seata/pull/7283)] optimize: use retry logic to end global trx


### security:
- [[#6069](https://github.com/apache/incubator-seata/pull/6069)] Upgrade Guava dependencies to fix security vulnerabilities
- [[#6145](https://github.com/apache/incubator-seata/pull/6145)] upgrade jettison to 1.5.4
- [[#6144](https://github.com/apache/incubator-seata/pull/6144)] upgrade nacos client to 1.4.6
- [[#6147](https://github.com/apache/incubator-seata/pull/6147)] upgrade kafka-clients to 3.6.1
- [[#6338](https://github.com/apache/incubator-seata/pull/6338)] upgrade jackson version
- [[#7201](https://github.com/apache/incubator-seata/pull/7202)] upgrade protobuf.version to 3.25.5
- [[#7214](https://github.com/apache/incubator-seata/pull/7214)] upgrade jackson to 2.18.3
- [[#7249](https://github.com/apache/incubator-seata/pull/7249)] upgrade axios to 1.8.2

### test:

- [[#7092](https://github.com/apache/incubator-seata/pull/7092)] fix the issue of NacosMockTest failing to run
- [[#7098](https://github.com/apache/incubator-seata/pull/7098)] Add unit tests for the `seata-common` module
- [[#7160](https://github.com/apache/incubator-seata/pull/7160)] Refactored tests in `LowerCaseLinkHashMapTest` to use parameterized unit testing
- [[#7167](https://github.com/apache/incubator-seata/pull/7167)] Refactored tests in `DurationUtilTest` to simplify and use parameterized unit testing
- [[#7189](https://github.com/apache/incubator-seata/pull/7189)] fix the runtime exception in the saga test case
- [[#7197](https://github.com/apache/incubator-seata/pull/7197)] add some UT cases for config module
- [[#7199](https://github.com/apache/incubator-seata/pull/7199)] add some UT cases for client processor
- [[#7203](https://github.com/apache/incubator-seata/pull/7203)] Refactored tests in rm.datasource.sql.Druid and seata-sqlparser-druid module
- [[#7221](https://github.com/apache/incubator-seata/pull/7221)] add UT for gRPC Encoder/Decode
- [[#7227](https://github.com/apache/incubator-seata/pull/7227)] add mock test for seata-discovery-consul module
- [[#7233][https://github.com/apache/incubator-seata/pull/7233]] add mock test for seata-discovery-etcd3
- [[#7243](https://github.com/apache/incubator-seata/pull/7243)] add unit test for seata-discovery-eureka
- [[#7255](https://github.com/apache/incubator-seata/pull/7255)] more unit tests for Discovery-Eureka

### refactor:

- [[#7145](https://github.com/apache/incubator-seata/pull/7145)] refactor the code that does not comply with license requirements
- [[#7236](https://github.com/apache/incubator-seata/pull/7236)] changed folder name in org.apache.seata.server.storage.raft.sore from sore to store


### doc:
- [[#7226](https://github.com/apache/incubator-seata/pull/7226)] write better docs for CONTRIBUTING.md


Thanks to these contributors for their code commits. Please report an unintended omission.

<!-- Please make sure your Github ID is in the list below -->

- [slievrly](https://github.com/slievrly)
- [lyl2008dsg](https://github.com/lyl2008dsg)
- [remind](https://github.com/remind)
- [lightClouds917](https://github.com/lightClouds917)
- [GoodBoyCoder](https://github.com/GoodBoyCoder)
- [PeppaO](https://github.com/PeppaO)
- [xjlgod](https://github.com/xjlgod)
- [funky-eyes](https://github.com/funky-eyes)
- [MaoMaoandSnail](https://github.com/MaoMaoandSnail)
- [psxjoy](https://github.com/psxjoy)
- [xiaoxiangyeyu0](https://github.com/xiaoxiangyeyu0)
- [wxrqforever](https://github.com/wxrqforever)
- [xingfudeshi](https://github.com/xingfudeshi)
- [YongGoose](https://github.com/YongGoose)
- [Monilnarang](https://github.com/Monilnarang)
- [iAmClever](https://github.com/iAmClever)
- [s-ramyalakshmi](https://github.com/s-ramyalakshmi)
- [YoWuwuuuw](https://github.com/YoWuwuuuw)
- [mehedikhan72](https://github.com/mehedikhan72)
- [AndrewSf](https://github.com/andrewseif)
- [bigcyy](https://github.com/bigcyy)
- [wjwang00](https://github.com/wjwang00)

Also, we receive many valuable issues, questions and advices from our community. Thanks for you all.

</details>
