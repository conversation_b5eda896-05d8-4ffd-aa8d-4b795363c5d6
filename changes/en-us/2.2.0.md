<!--
    Licensed to the Apache Software Foundation (ASF) under one or more
    contributor license agreements.  See the NOTICE file distributed with
    this work for additional information regarding copyright ownership.
    The ASF licenses this file to You under the Apache License, Version 2.0
    (the "License"); you may not use this file except in compliance with
    the License.  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0
    
    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
-->
Add changes here for all PR submitted to the 2.x branch.

<!-- Please add the `changes` to the following location(feature/bugfix/optimize/test) based on the type of PR -->

### feature:
- [[#6536](https://github.com/apache/incubator-seata/pull/6536)] support naming server client
- [[#6226](https://github.com/apache/incubator-seata/pull/6226)] multi-version seata protocol support
- [[#6537](https://github.com/apache/incubator-seata/pull/6537)] support Namingserver
- [[#6538](https://github.com/apache/incubator-seata/pull/6538)] Integration of naming server on the Seata server side
- [[#6766](https://github.com/apache/incubator-seata/pull/6766)] add TCC three-phase hooks

### bugfix:
- [[#6592](https://github.com/apache/incubator-seata/pull/6592)] fix @Async annotation not working in ClusterWatcherManager
- [[#6624](https://github.com/apache/incubator-seata/pull/6624)] fix Alibaba Dubbo convert error
- [[#6627](https://github.com/apache/incubator-seata/pull/6627)] fix the issue of xaEnded not being reset
- [[#6626](https://github.com/apache/incubator-seata/pull/6626)] fix hsf ConsumerModel convert error
- [[#6816](https://github.com/apache/incubator-seata/pull/6816)] Fix NPE when getting branchType in a non-global transaction context
- [[#6642](https://github.com/apache/incubator-seata/pull/6642)] codecov token not found
- [[#6661](https://github.com/apache/incubator-seata/pull/6661)] fix `tableMeta` cache scheduled refresh issue
- [[#6486](https://github.com/apache/incubator-seata/pull/6486)] fix mysql undo log update sql data more than max allowed packet
- [[#6668](https://github.com/apache/incubator-seata/pull/6668)] thread safety issue when adding and removing instances
- [[#6678](https://github.com/apache/incubator-seata/pull/6678)] fix the same record has different lowkeys due to mixed case of table names yesterday
- [[#6697](https://github.com/apache/incubator-seata/pull/6697)] v0 ByteBuf should not decode by super class
- [[#6707](https://github.com/apache/incubator-seata/pull/6707)] fix readonly branch commit errors in Oracle XA transactions
- [[#6711](https://github.com/apache/incubator-seata/pull/6711)] fix dameng rollback info un compress fail
- [[#6714](https://github.com/apache/incubator-seata/pull/6714)] fix dameng delete undo fail
- [[#6511](https://github.com/apache/incubator-seata/pull/6511)] fix the failure of rollbacking back of the delete SQL at AT mode when using SQLServer
- [[#6701](https://github.com/apache/incubator-seata/pull/6728)] fix support serialization for dm.jdbc.driver.DmdbTimestamp
- [[#6757](https://github.com/apache/incubator-seata/pull/6757)] the bug where multiple nodes cannot be retrieved from the naming server
- [[#6769](https://github.com/apache/incubator-seata/pull/6769)] fix tcc fence deadLock
- [[#6778](https://github.com/apache/incubator-seata/pull/6778)] fix namingserver node term
- [[#6765](https://github.com/apache/incubator-seata/pull/6765)] fix MySQL driver loading by replacing custom classloader with system classloader for better compatibility and simplified process
- [[#6781](https://github.com/apache/incubator-seata/pull/6781)] the issue where the TC occasionally fails to go offline from the NamingServer
- [[#6785](https://github.com/apache/incubator-seata/pull/6785)] fix prometheus fail to return seata metrics data when using Nacos
- [[#6797](https://github.com/apache/incubator-seata/pull/6797)] fall back to any of available cluster address when query cluster address is empty
- [[#6800](https://github.com/apache/incubator-seata/pull/6800)] make exception message generic for all database drivers
- [[#6759](https://github.com/apache/incubator-seata/pull/6759)] fix the error of active refresh failure of cross-database table metadata
- [[#6812](https://github.com/apache/incubator-seata/pull/6812)] bugfix: change group and node offline status are not pushed in real time
- [[#6817](https://github.com/apache/incubator-seata/pull/6817)] bugfix: fix namingserver changVgroup failed
- [[#6820](https://github.com/apache/incubator-seata/pull/6820)] Fix file path error in the Dockerfile
- [[#6825](https://github.com/apache/incubator-seata/pull/6825)] Fix the issue of XA mode transaction timeout and inability to roll back in Postgres
- [[#6833](https://github.com/apache/incubator-seata/pull/6833)] SQLIntegrityConstraintViolationException capture incorrectly when inserting a globallock
- [[#6835](https://github.com/apache/incubator-seata/pull/6835)] Fix the issue of missing request body of post method in HttpClientUtil
- [[#6845](https://github.com/apache/incubator-seata/pull/6845)] fix rocksDB opens the same file multiple times
- [[#6840](https://github.com/apache/incubator-seata/pull/6840)] Fix the issue of unsafe deserialization in ProcessorYaml.java
- [[#6843](https://github.com/apache/incubator-seata/pull/6843)] Fix 403 error when sending a POST request from the console
- [[#6850](https://github.com/apache/incubator-seata/pull/6850)] raft mode is backward compatible with version 2.0
- [[#6855](https://github.com/apache/incubator-seata/pull/6855)] after scaling down a Raft cluster, the metadata still contains the removed node
- [[#6859](https://github.com/apache/incubator-seata/pull/6859)] remove duplicated dependency in pom


### optimize:
- [[#6499](https://github.com/apache/incubator-seata/pull/6499)] split the task thread pool for committing and rollbacking statuses
- [[#6208](https://github.com/apache/incubator-seata/pull/6208)] optimize : load SeataSerializer by version
- [[#6209](https://github.com/apache/incubator-seata/pull/6209)] Eliminate RpcMessage and Encoder/Decoder dependencies
- [[#6634](https://github.com/apache/incubator-seata/pull/6634)] select channel handles based on protocol versions
- [[#6523](https://github.com/apache/incubator-seata/pull/6523)] upgrade alibaba/druid version to 1.2.20
- [[#6566](https://github.com/apache/incubator-seata/pull/6566)] Add support for configuring exposeProxy in GlobalTransactionScanner
- [[#6534](https://github.com/apache/incubator-seata/pull/6534)] optimize: send async response
- [[#6640](https://github.com/apache/incubator-seata/pull/6640)] modify codecov config
- [[#6640](https://github.com/apache/incubator-seata/pull/6648)] add license header
- [[#6666](https://github.com/apache/incubator-seata/pull/6666)] add ExceptionUtil class for unwarp error msg
- [[#6654](https://github.com/apache/incubator-seata/pull/6654)] add Namingserver package module
- [[#6667](https://github.com/apache/incubator-seata/pull/6667)] optimize Namingserver log output
- [[#6687](https://github.com/apache/incubator-seata/pull/6687)] delete static code built on the frontend
- [[#6700](https://github.com/apache/incubator-seata/pull/6700)] remove sdk version checking
- [[#6727](https://github.com/apache/incubator-seata/pull/6727)] deserialize performance optimize
- [[#6732](https://github.com/apache/incubator-seata/pull/6732)] add the default console and security config on application.example.yml and application.raft.example.yml
- [[#6651](https://github.com/apache/incubator-seata/pull/6651)] add license header for proto file
- [[#6653](https://github.com/apache/incubator-seata/pull/6653)] optimize multiple licenses and remove license urls
- [[#6655](https://github.com/apache/incubator-seata/pull/6655)] update front-end license
- [[#6652](https://github.com/apache/incubator-seata/pull/6673)] add license header for spring config file
- [[#6674](https://github.com/apache/incubator-seata/pull/6674)] update source license
- [[#6650](https://github.com/apache/incubator-seata/pull/6650)] add license header for SPI file
- [[#6741](https://github.com/apache/incubator-seata/pull/6741)] upgrade tomcat-embed-core to 9.0.90
- [[#6742](https://github.com/apache/incubator-seata/pull/6742)] upgrade npmjs version in console
- [[#6743](https://github.com/apache/incubator-seata/pull/6743)] upgrade npmjs version in saga
- [[#6746](https://github.com/apache/incubator-seata/pull/6746)] optimize compatible dependencies
- [[#6745](https://github.com/apache/incubator-seata/pull/6745)] fix node-gyp build error on arm64 and macos
- [[#6749](https://github.com/apache/incubator-seata/pull/6749)] optimize WebSecurityConfig csrf
- [[#6748](https://github.com/apache/incubator-seata/pull/6748)] optimize ConsistentHashLoadBalance Algorithm
- [[#6747](https://github.com/apache/incubator-seata/pull/6747)] optimize fastjson deserialization
- [[#6755](https://github.com/apache/incubator-seata/pull/6755)] optimize namingserver code logic
- [[#6763](https://github.com/apache/incubator-seata/pull/6763)] optimize NacosConfiguration singleton reload
- [[#6761](https://github.com/apache/incubator-seata/pull/6761)] optimize the namingserver code to improve readability
- [[#6768](https://github.com/apache/incubator-seata/pull/6768)] report the tcc fence transaction isolation level
- [[#6770](https://github.com/apache/incubator-seata/pull/6770)] Automatic deletion of namingserver vgroup through Caffeine map
- [[#6780](https://github.com/apache/incubator-seata/pull/6780)] optimize the reflection operation in class `SerializerServiceLoader`
- [[#6784](https://github.com/apache/incubator-seata/pull/6784)] upgrade axios to 1.7.4
- [[#6787](https://github.com/apache/incubator-seata/pull/6787)] upgrade elliptic to 6.5.7
- [[#6783](https://github.com/apache/incubator-seata/pull/6783)] rename the server naming/v1 api to vgroup/v1
- [[#6793](https://github.com/apache/incubator-seata/pull/6793)] fix npmjs conflicts
- [[#6793](https://github.com/apache/incubator-seata/pull/6795)] optimize the initialization logic for server meta
- [[#6794](https://github.com/apache/incubator-seata/pull/6794)] optimize NacosMockTest UT case
- [[#6806](https://github.com/apache/incubator-seata/pull/6806)] optimize `tableMeta` cache scheduled refresh issue
- [[#6808](https://github.com/apache/incubator-seata/pull/6808)] change version to 2.2.0-SNAPSHOT
- [[#6819](https://github.com/apache/incubator-seata/pull/6819)] merge the packaging processes of namingserver and seata-server
- [[#6827](https://github.com/apache/incubator-seata/pull/6827)] rename namingserver registry type
- [[#6836](https://github.com/apache/incubator-seata/pull/6836)] add independent nacos for the CI process
- [[#6841](https://github.com/apache/incubator-seata/pull/6841)] update the LICENSE and NOTICE files and standardize dependency versions
- [[#6823](https://github.com/apache/incubator-seata/pull/6823)] fix typo
- [[#6779](https://github.com/apache/incubator-seata/pull/6779)] use curator instead of zkclient in config model
- [[#6831](https://github.com/apache/incubator-seata/pull/6831)] use curator instead of zkclient in registry model
- [[#6852](https://github.com/apache/incubator-seata/pull/6852)] optimize raft metadata api
- [[#6863](https://github.com/apache/incubator-seata/pull/6863)] update NOTICE.md

### refactor:

### security:

### test:
- [[#6533](https://github.com/apache/incubator-seata/pull/6533)] increase integration-tx-api module unit test coverage
- [[#6608](https://github.com/apache/incubator-seata/pull/6608)] add unit test for sql-parser-core
- [[#6647](https://github.com/apache/incubator-seata/pull/6647)] improve the test case coverage of saga module to 70%
- [[#6695](https://github.com/apache/incubator-seata/pull/6695)] old version(< 0.7.1) client test case for multi-version protocol
- [[#6752](https://github.com/apache/incubator-seata/pull/6752)] Improve the test case coverage of metrics module
- [[#6764](https://github.com/apache/incubator-seata/pull/6764)] add Apollo mock test case
- [[#6750](https://github.com/apache/incubator-seata/pull/6750)] increase spring autoconfigure module unit test converage
- [[#6773](https://github.com/apache/incubator-seata/pull/6773)] fix the wrong code coverage from codecov icon in default branch
- [[#6821](https://github.com/apache/incubator-seata/pull/6821)] fix the test case assertions
- [[#6803](https://github.com/apache/incubator-seata/pull/6803)] optimize: compilation and packaging for the ARM64 architecture


Thanks to these contributors for their code commits. Please report an unintended omission.

<!-- Please make sure your Github ID is in the list below -->
- [slievrly](https://github.com/slievrly)
- [tuwenlin](https://github.com/tuwenlin)
- [YeonCheolGit](https://github.com/YeonCheolGit)
- [liuqiufeng](https://github.com/liuqiufeng)
- [God-Gan](https://github.com/God-Gan)
- [Bughue](https://github.com/Bughue)
- [funky-eyes](https://github.com/funky-eyes)
- [tanyaofei](https://github.com/tanyaofei)
- [traitsisgiorgos](https://github.com/traitsisgiorgos)
- [wanghongzhou](https://github.com/wanghongzhou)
- [ggbocoder](https://github.com/ggbocoder)
- [azatyamanaev](https://github.com/azatyamanaev)
- [xjlgod](https://github.com/xjlgod)
- [xingfudeshi](https://github.com/xingfudeshi)
- [wuwen5](https://github.com/wuwen5)
- [jsbxyyx](https://github.com/jsbxyyx)
- [iAmClever](https://github.com/iAmClever)
- [GoodBoyCoder](https://github.com/GoodBoyCoder)
- [liuqiufeng](https://github.com/liuqiufeng)
- [caohdgege](https://github.com/caohdgege)
- [TakeActionNow2019](https://github.com/TakeActionNow2019)
- [imashimaro](https://github.com/hmj776521114)
- [lyl2008dsg](https://github.com/lyl2008dsg)
- [lightClouds917](https://github.com/lightClouds917)
- [l81893521](https://github.com/l81893521)
- [laywin](https://github.com/laywin)
- [xiaoxiangyeyu0](https://github.com/xiaoxiangyeyu0)
- [LegGasai](https://github.com/LegGasai)
- [yangli-stu](https://github.com/yangli-stu)
- [heliang666s](https://github.com/heliang666s)


Also, we receive many valuable issues, questions and advices from our community. Thanks for you all.
