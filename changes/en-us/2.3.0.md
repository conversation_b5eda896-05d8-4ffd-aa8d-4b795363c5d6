<!--
    Licensed to the Apache Software Foundation (ASF) under one or more
    contributor license agreements.  See the NOTICE file distributed with
    this work for additional information regarding copyright ownership.
    The ASF licenses this file to You under the Apache License, Version 2.0
    (the "License"); you may not use this file except in compliance with
    the License.  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0
    
    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
-->
### 2.3.0

<details>
  <summary><mark>Release notes</mark></summary>	

### Apache Seata(incubating) 2.3.0

Seata 2.3.0 Released.

Seata is an easy-to-use, high-performance, open source distributed transaction solution.

The version is updated as follows:

### feature:

- [[#6904](https://github.com/apache/incubator-seata/pull/6904)] add fastjson2 serializer support
- [[#6876](https://github.com/apache/incubator-seata/pull/6876)] support kingbase
- [[#6881](https://github.com/apache/incubator-seata/pull/6881)] support grpc
- [[#6864](https://github.com/apache/incubator-seata/pull/6864)] support shentong database
- [[#6974](https://github.com/apache/incubator-seata/pull/6974)] support fastjson2 undolog parser
- [[#6992](https://github.com/apache/incubator-seata/pull/6992)] support grpc serializer
- [[#6973](https://github.com/apache/incubator-seata/pull/6973)] support saga annotation
- [[#6926](https://github.com/apache/incubator-seata/pull/6926)] support ssl communication for raft nodes

### bugfix:

- [[#6899](https://github.com/apache/incubator-seata/pull/6899)] fix file.conf read failed after package
- [[#6890](https://github.com/apache/incubator-seata/pull/6890)] fix designerJson to standardJson: subStateMachine
  compensateState cannot be recognized
- [[#6907](https://github.com/apache/incubator-seata/pull/6907)] fix the issue of Codecov not generating reports
- [[#6923](https://github.com/apache/incubator-seata/pull/6923)] Enhance 401 Error Handling by Refreshing Token
- [[#6925](https://github.com/apache/incubator-seata/pull/6925)] fix the issue in Raft model a follower's crash may lead
  to the continued use of expired tokens
- [[#6932](https://github.com/apache/incubator-seata/pull/6932)] when enabling local transactions, the lock contention
  failure in file & raft mode does not exit, leading to a lingering lock
- [[#6940](https://github.com/apache/incubator-seata/pull/6940)] Fix NacosRegistry lookup behavior
  transactionServiceGroup is empty causing NPE error
- [[#6943](https://github.com/apache/incubator-seata/pull/6943)] fix the conversion error for `convertBranchSession` in
  concurrent environment.
- [[#6948](https://github.com/apache/incubator-seata/pull/6948)] Fix the CI build issue on the ARM64 platform
- [[#6947](https://github.com/apache/incubator-seata/pull/6947)] fix npe for nacos registry when look up address
- [[#6984](https://github.com/apache/incubator-seata/pull/6984)] support building docker image on openjdk23
- [[#6994](https://github.com/apache/incubator-seata/pull/6994)] fix the problem of building undoLog exception when
  update join does not update data
- [[#7005](https://github.com/apache/incubator-seata/pull/7005)] fix the Raft NPE issue caused by two-phase concurrency
- [[#7010](https://github.com/apache/incubator-seata/pull/7010)] fix error while the "context" is key word in DM8 when
  delete undolog
- [[#7022](https://github.com/apache/incubator-seata/pull/7022)] fix `store.mode` property
  in `application.raft.example.yml`
- [[#7025](https://github.com/apache/incubator-seata/pull/7025)] fix vGroupMappingManager is NOT init
- [[#7044](https://github.com/apache/incubator-seata/pull/7044)] fix tableMeta refresh after closed
- [[#7117](https://github.com/apache/incubator-seata/pull/7117)] fix prefix: seata.server.raft.ssl should not be null
- [[#7127](https://github.com/apache/incubator-seata/pull/7127)] fix branchType server decode error


### optimize:

- [[#6826](https://github.com/apache/incubator-seata/pull/6826)] remove the branch registration operation of the XA
  read-only transaction
- [[#6874](https://github.com/apache/incubator-seata/pull/6874)] modify the version to 2.3.0-SNAPSHOT
- [[#6892](https://github.com/apache/incubator-seata/pull/6892)] upgrade npmjs version in console module
- [[#6883](https://github.com/apache/incubator-seata/pull/6874)] remove write only object
- [[#6892](https://github.com/apache/incubator-seata/pull/6892)] upgrade npmjs version
- [[#6889](https://github.com/apache/incubator-seata/pull/6889)] Correct word spelling errors
- [[#6898](https://github.com/apache/incubator-seata/pull/6898)] upgrade npmjs version in saga module
- [[#6879](https://github.com/apache/incubator-seata/pull/6879)] fix log argument mismatch issue
- [[#6902](https://github.com/apache/incubator-seata/pull/6900)] optimize readme docs
- [[#6807](https://github.com/apache/incubator-seata/pull/6807)] splitting MergedWarpMessage enhances the server
  parallel processing capability
- [[#6905](https://github.com/apache/incubator-seata/pull/6905)] remove incompatible licenses at build time
- [[#6906](https://github.com/apache/incubator-seata/pull/6906)] h2 dependency adds test scope
- [[#6911](https://github.com/apache/incubator-seata/pull/6911)] fix some typos in project
- [[#6918](https://github.com/apache/incubator-seata/pull/6918)] Use the openjdk image of eclipse-temurin as the base
  image
- [[#6938](https://github.com/apache/incubator-seata/pull/6938)] Update online chat information in README.md
- [[#6950](https://github.com/apache/incubator-seata/pull/6950)] Remove JVM parameter app.id
- [[#6959](https://github.com/apache/incubator-seata/pull/6959)] update the naming and description for
  the `seata-http-jakarta` module
- [[#6991](https://github.com/apache/incubator-seata/pull/6991)] gRPC serialization default to Protobuf
- [[#6993](https://github.com/apache/incubator-seata/pull/6993)] optimize transaction metrics
- [[#6995](https://github.com/apache/incubator-seata/pull/6995)] upgrade outdate npmjs dependencies
- [[#6996](https://github.com/apache/incubator-seata/pull/6996)] optimize lock release logic in AT transaction mode
- [[#7023](https://github.com/apache/incubator-seata/pull/7023)] optimize fail fast, when all server not available
- [[#7027](https://github.com/apache/incubator-seata/pull/7027)] raft mode maintains the reload logic consistent with
  the file
- [[#6891](https://github.com/apache/incubator-seata/pull/6891)] add StateType Enum
- [[#7040](https://github.com/apache/incubator-seata/pull/7040)] optimize the print info in ConfigurationFactory
- [[#7046](https://github.com/apache/incubator-seata/pull/7046)] remove the dependency conflict for spring-webmvc
- [[#7043](https://github.com/apache/incubator-seata/pull/7043)] finish rollback if sendResult/msg not found
- [[#7051](https://github.com/apache/incubator-seata/pull/7051)] add namingserver jib
- [[#7054](https://github.com/apache/incubator-seata/pull/7054)] In file mode when the lock cannot be acquired output
  the holder's xid
- [[#7154](https://github.com/apache/incubator-seata/pull/7154)] remove unused dependencies
- [[#7153](https://github.com/apache/incubator-seata/pull/7153)] upgrade tomcat-embed to 9.0.98
- [[#7152](https://github.com/apache/incubator-seata/pull/7152)] remove org.eclipse.jetty dependency
- [[#7151](https://github.com/apache/incubator-seata/pull/7151)] upgrade xstream to 1.4.21

### refactor:

- [[#7017](https://github.com/apache/incubator-seata/pull/7017)] remove dependency on seata-server module
- [[#7155](https://github.com/apache/incubator-seata/pull/7155)] refactor the code that does not comply with license requirements

### test:

- [[#6869](https://github.com/apache/incubator-seata/pull/6869)] Add unit tests for the `seata-core` module
- [[#6927](https://github.com/apache/incubator-seata/pull/6927)] Add unit tests for the `seata-rocketmq` module
- [[#7018](https://github.com/apache/incubator-seata/pull/7018)] Add unit tests for the `seata-tm` module
- [[#7030](https://github.com/apache/incubator-seata/pull/7030)] Add unit tests for the `seata-common` module

Thanks to these contributors for their code commits. Please report an unintended omission.

<!-- Please make sure your Github ID is in the list below -->

- [slievrly](https://github.com/slievrly)
- [GoodBoyCoder](https://github.com/GoodBoyCoder)
- [funky-eyes](https://github.com/funky-eyes)
- [dk2k](https://github.com/dk2k)
- [MaoMaoandSnail](https://github.com/MaoMaoandSnail)
- [yougecn](https://github.com/yougecn)
- [arrrnold17](https://github.com/arrrnold17)
- [xjlgod](https://github.com/xjlgod)
- [PleaseGiveMeTheCoke](https://github.com/PleaseGiveMeTheCoke)
- [dsomehan](https://github.com/dsomehan)
- [psxjoy](https://github.com/psxjoy)
- [xingfudeshi](https://github.com/xingfudeshi)
- [o-jimin](https://github.com/o-jimin)
- [lixingjia77](https://github.com/lixingjia77)
- [whaon](https://github.com/whaon)
- [YvCeung](https://github.com/YvCeung)
- [jsbxyyx](https://github.com/jsbxyyx)
- [lightClouds917](https://github.com/lightClouds917)
- [Muluo-cyan](https://github.com/Muluo-cyan)
- [yixia](https://github.com/wt-better)
- [ChinaJeckXu](https://github.com/ChinaJeckXu)

Also, we receive many valuable issues, questions and advices from our community. Thanks for you all.
