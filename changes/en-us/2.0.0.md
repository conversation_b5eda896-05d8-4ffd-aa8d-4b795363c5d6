<!--
    Licensed to the Apache Software Foundation (ASF) under one or more
    contributor license agreements.  See the NOTICE file distributed with
    this work for additional information regarding copyright ownership.
    The ASF licenses this file to You under the Apache License, Version 2.0
    (the "License"); you may not use this file except in compliance with
    the License.  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0
    
    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
-->
### 2.0.0

 [source](https://github.com/seata/seata/archive/v2.0.0.zip) |	
 [binary](https://github.com/seata/seata/releases/download/v2.0.0/seata-server-2.0.0.zip) 	

<details>
  <summary><mark>Release notes</mark></summary>	

### Seata 2.0.0

Seata 2.0.0 Released.

Seata is an easy-to-use, high-performance, open source distributed transaction solution.

The version is updated as follows:

### feature：
- [[#5165](https://github.com/seata/seata/pull/5165)] optimize TCC structure, supporting API access. add integration layer module(seata-integration-tx-api) for transaction process definition and proxy enhancement.
- [[#5352](https://github.com/seata/seata/pull/5352)] add jackson json parser and gson json parser for tcc business action context
- [[#5377](https://github.com/seata/seata/pull/5377)] make AbstractHttpExecutor.class support http put
- [[#5396](https://github.com/seata/seata/pull/5396)] TC log appender metric
- [[#5118](https://github.com/seata/seata/pull/5118)] support two-stage concurrent notification execution
- [[#5529](https://github.com/seata/seata/pull/5529)] docker image supports JVM parameter injection
- [[#3887](https://github.com/seata/seata/pull/3887)] add SQL Server database support in AT mode
- [[#4033](https://github.com/seata/seata/pull/4033)] add SQLServer support for Server DB storage mode
- [[#5717](https://github.com/seata/seata/pull/5717)] compatible with file.conf and registry.conf configurations in version 1.4.2 and below
- [[#5842](https://github.com/seata/seata/pull/5842)] adding metainfo to docker image
- [[#5902](https://github.com/seata/seata/pull/5902)] support IPv6
- [[#5907](https://github.com/seata/seata/pull/5907)] support polardb-x 2.0 in AT mode
- [[#5932](https://github.com/seata/seata/pull/5932)] support Dameng database
- [[#5946](https://github.com/seata/seata/pull/5946)] add sqlserver's adaptation to the console paging interface
- [[#5226](https://github.com/seata/seata/pull/5226)] support raft cluster and store mode

### bugfix：
- [[#5677](https://github.com/seata/seata/pull/5677)] fix saga mode serviceTask inputParams json autoType convert exception
- [[#5194](https://github.com/seata/seata/pull/5194)] fix wrong keyword order for oracle when creating a table
- [[#5021](https://github.com/seata/seata/pull/5201)] fix JDK Reflection for Spring origin proxy failed in JDK17
- [[#5023](https://github.com/seata/seata/pull/5203)] fix `seata-core` dependency transitive conflict in `seata-dubbo`
- [[#5224](https://github.com/seata/seata/pull/5224)] fix oracle initialize script index_name is duplicate
- [[#5233](https://github.com/seata/seata/pull/5233)] fix the inconsistent configuration item names related to LoadBalance
- [[#5245](https://github.com/seata/seata/pull/5245)] fix the incomplete dependency of distribution module
- [[#5239](https://github.com/seata/seata/pull/5239)] fix `getConfig` throw `ClassCastException` when use JDK proxy
- [[#5266](https://github.com/seata/seata/pull/5265)] fix server console has queried the released lock
- [[#5282](https://github.com/seata/seata/pull/5282)] parallel request handle throw IndexOutOfBoundsException
- [[#5294](https://github.com/seata/seata/pull/5294)] fix auto-increment of pk columns in PostgreSQL/Oracle in AT mode
- [[#5298](https://github.com/seata/seata/pull/5298)] don't remove GlobalSession when retry rollback or retry commit timeout
- [[#5304](https://github.com/seata/seata/pull/5304)] remove RollbackRetryTimeout sessions during in file storage recover
- [[#5310](https://github.com/seata/seata/pull/5310)] fix that keywords don't add escaped characters
- [[#5318](https://github.com/seata/seata/pull/5318)] fix G1 jvm parameter in jdk8
- [[#5330](https://github.com/seata/seata/pull/5330)] fix bugs found in unit tests
- [[#5337](https://github.com/seata/seata/pull/5337)] fix bugs found in feature#5165 about sorting problem of multiple interceptor under the spring used environment, by the way fix the BeforeTransaction(AfterTransaction) transaction ordering problem when the order is consistent
- [[#5347](https://github.com/seata/seata/pull/5347)] Fix console print `unauthorized error`
- [[#5355](https://github.com/seata/seata/pull/5355)] fix bug when customizing context-path
- [[#5362](https://github.com/seata/seata/pull/5362)] fix When the rollback logic on the TC side returns RollbackFailed, the custom FailureHandler is not executed
- [[#5372](https://github.com/seata/seata/pull/5372)] fix transaction timeout on client side not execute hook and failureHandler
- [[#4734](https://github.com/seata/seata/pull/4734)] check if table meta cache should be refreshed in AT mode
- [[#5426](https://github.com/seata/seata/pull/5426)] fix the GlobalTransactional annotation npe issue.
- [[#5464](https://github.com/seata/seata/pull/5464)] fix global session is always begin in saga mode
- [[#5478](https://github.com/seata/seata/pull/5478)] fix finished transaction swallows exception when committing
- [[#5491](https://github.com/seata/seata/pull/5491)] fix method name not print in logs
- [[#5449](https://github.com/seata/seata/pull/5449)] fix Oracle XA transaction reentrant issues
- [[#5531](https://github.com/seata/seata/pull/5531)] fix the log file path was loaded incorrectly
- [[#5523](https://github.com/seata/seata/pull/5523)] fix GlobalStatus=9 can't be cleared in DB storage mode
- [[#5558](https://github.com/seata/seata/pull/5558)] fix mariadb rollback failed
- [[#5556](https://github.com/seata/seata/pull/5556)] fix oracle insert undolog failed
- [[#5579](https://github.com/seata/seata/pull/5579)] fix RM_CHANNELS get npe when resourceId is empty
- [[#5577](https://github.com/seata/seata/pull/5577)] fix grpc interceptor xid unbinding problem
- [[#5594](https://github.com/seata/seata/pull/5594)] fix log in participant transaction role
- [[#5604](https://github.com/seata/seata/pull/5604)] fix the `asyncCommit` and `queueToRetryCommit` always failed in db mode
- [[#5661](https://github.com/seata/seata/pull/5661)] bugfix: the timeout is null when the connectionProxyXA connection is reused
- [[#5678](https://github.com/seata/seata/pull/5675)] bugfix: fix compatibility between xxx.grouplist and grouplist.xxx configuration items
- [[#5715](https://github.com/seata/seata/pull/5715)] fix get configuration item contains underlined error
- [[#5748](https://github.com/seata/seata/pull/5748)] case of the pk col-name in the business sql is inconsistent with the case in the table metadata, resulting in a rollback failure
- [[#5745](https://github.com/seata/seata/pull/5745)] fix the problem that the parameter prefix requirement of the setAttachment method in sofa-rpc is not met
- [[#5772](https://github.com/seata/seata/pull/5762)] change some fields type of TableMetaCache to avoid integer overflow
- [[#5787](https://github.com/seata/seata/pull/5794)] Solution cluster cannot be customized when redis serves as the registry
- [[#5810](https://github.com/seata/seata/pull/5810)] fix XA transaction start exception and rollback failure caused by druid dependency conflict
- [[#5821](https://github.com/seata/seata/pull/5821)] fix insert executor keywords unescape
- [[#5835](https://github.com/seata/seata/pull/5835)] bugfix: fix TC retry rollback wrongly, after the XA transaction fail and rollback
- [[#5881](https://github.com/seata/seata/pull/5880)] fix delete branch table unlock failed
- [[#5930](https://github.com/seata/seata/pull/5930)] fix the issue of missing sentinel password in store redis mode
- [[#5958](https://github.com/seata/seata/pull/5958)] required to be unlocked when a re-election occurs in a commit state
- [[#5971](https://github.com/seata/seata/pull/5971)] fix some configurations that are not deprecated show "Deprecated"
- [[#5977](https://github.com/seata/seata/pull/5977)] fix that rpcserver is not closed when raftServer is closed
- [[#5954](https://github.com/seata/seata/pull/5954)] fix the issue of saved branch session status does not match the actual branch session status
- [[#5990](https://github.com/seata/seata/pull/5990)] fix the issue that the Lua script is not synchronized when the redis sentinel master node is down
- [[#5887](https://github.com/seata/seata/pull/5887)] fix global transaction hook repeat execute
- [[#6018](https://github.com/seata/seata/pull/6018)] fix incorrect metric report
- [[#6024](https://github.com/seata/seata/pull/6024)] fix the white screen after click the "View Global Lock" button on the transaction info page in the console
- [[#6015](https://github.com/seata/seata/pull/6015)] fix can't integrate dubbo with spring
- [[#6049](https://github.com/seata/seata/pull/6049)] fix registry type for raft under the network interruption did not carry out the sleep 1s
- [[#6050](https://github.com/seata/seata/pull/6050)] change RaftServer#destroy to wait all shutdown procedures done

### optimize：
- [[#6033](https://github.com/seata/seata/pull/6033)] optimize the isReference judgment logic in HSFRemotingParser, remove unnecessary judgment about FactoryBean
- [[#5966](https://github.com/seata/seata/pull/5966)] decouple saga expression handling and remove evaluator package
- [[#5928](https://github.com/seata/seata/pull/5928)] add Saga statelang semantic validation
- [[#5208](https://github.com/seata/seata/pull/5208)] optimize throwable getCause once more
- [[#5212](https://github.com/seata/seata/pull/5212)] optimize log message level
- [[#5237](https://github.com/seata/seata/pull/5237)] optimize exception log message print(EnhancedServiceLoader.loadFile#cahtch)
- [[#5243](https://github.com/seata/seata/pull/5243)] optimize kryo 5.4.0 optimize compatibility with jdk17
- [[#5153](https://github.com/seata/seata/pull/5153)] Only AT mode try to get channel with other app
- [[#5177](https://github.com/seata/seata/pull/5177)] If `server.session.enable-branch-async-remove` is true, delete the branch asynchronously and unlock it synchronously.
- [[#4858](https://github.com/seata/seata/pull/4858)] reorganize the usage of task session manager
- [[#4881](https://github.com/seata/seata/pull/4881)] reorganize the usage of Sessionmanager and listener
- [[#5273](https://github.com/seata/seata/pull/5273)] Optimize the compilation configuration of the `protobuf-maven-plugin` plug-in to solve the problem of too long command lines in higher versions.
- [[#5278](https://github.com/seata/seata/pull/5278)] clean multi-sessionmanager-instance pattern
- [[#5302](https://github.com/seata/seata/pull/5302)] remove startup script the -Xmn configuration
- [[#4880](https://github.com/seata/seata/pull/4880)] optimize logs when commit/rollback catch an exception
- [[#5322](https://github.com/seata/seata/pull/5322)] optimize the log of SPI
- [[#5323](https://github.com/seata/seata/pull/5323)] add time info for global transaction timeout log
- [[#5328](https://github.com/seata/seata/pull/5333)] add corresponding lua implementation for Redis mode of global transaction and transaction storage
- [[#5341](https://github.com/seata/seata/pull/5341)] optimize gRPC Interceptor for TCC mode
- [[#5342](https://github.com/seata/seata/pull/5342)] optimize the check of the delay value of the TCC fence log clean task
- [[#5325](https://github.com/seata/seata/pull/5325)] add store mode,config type and registry type log info
- [[#5351](https://github.com/seata/seata/pull/5351)] optimize RPC filter for TCC mode 
- [[#5354](https://github.com/seata/seata/pull/5354)] reconstruct the RPC integration module
- [[#5370](https://github.com/seata/seata/pull/5370)] optimize transaction fail handler
- [[#5461](https://github.com/seata/seata/pull/5461)] optimize license workflow
- [[#5456](https://github.com/seata/seata/pull/5456)] refactor ColumnUtils and EscapeHandler
- [[#5438](https://github.com/seata/seata/pull/5438)] optimize code style properties
- [[#5471](https://github.com/seata/seata/pull/5471)] optimize transaction log on client side
- [[#5485](https://github.com/seata/seata/pull/5485)] optimize server log output
- [[#4907](https://github.com/seata/seata/pull/4907)] optimize thread scheduling and code
- [[#5487](https://github.com/seata/seata/pull/5487)] mark the lockholder of branchsession as final
- [[#5519](https://github.com/seata/seata/pull/5519)] optimize FenceHandler for oracle
- [[#5501](https://github.com/seata/seata/pull/5501)] support updating transaction state with optimistic locking
- [[#5419](https://github.com/seata/seata/pull/5419)] optimize images based on java 8/17 and support maven-3.9.0
- [[#5549](https://github.com/seata/seata/pull/5549)] update expire gpg key and publish workflow
- [[#5576](https://github.com/seata/seata/pull/5576)] The common fence clean task is only initiated when useTCCFence is set to true
- [[#5623](https://github.com/seata/seata/pull/5623)] optimize possible conflict between asyncCommitting thread and retryCommitting thread
- [[#5553](https://github.com/seata/seata/pull/5553)] support case-sensitive attributes for table and column metadata
- [[#5644](https://github.com/seata/seata/pull/5644)] optimize server logs print
- [[#5680](https://github.com/seata/seata/pull/5680)] optimize escape character for case of columnNames
- [[#5714](https://github.com/seata/seata/pull/5714)] optimize distributed lock log
- [[#5723](https://github.com/seata/seata/pull/5723)] optimize docker default timezone
- [[#5779](https://github.com/seata/seata/pull/5779)] remove unnecessary log outputs and unify the log output path.
- [[#5802](https://github.com/seata/seata/pull/5802)] set server's transaction level to READ_COMMITTED
- [[#5783](https://github.com/seata/seata/pull/5783)] support the nacos application name property
- [[#5524](https://github.com/seata/seata/pull/5524)] support for more operational commands in seata-server.sh
- [[#5836](https://github.com/seata/seata/pull/5836)] separate MySQL from Mariadb implementations
- [[#5869](https://github.com/seata/seata/pull/5869)] some minor syntax optimization
- [[#5885](https://github.com/seata/seata/pull/5885)] optimize log in ConnectionProxyXA
- [[#5894](https://github.com/seata/seata/pull/5894)] remove dependency without license
- [[#5895](https://github.com/seata/seata/pull/5895)] remove 7z format compression support
- [[#5896](https://github.com/seata/seata/pull/5896)] remove mariadb.jdbc dependency
- [[#5384](https://github.com/seata/seata/pull/5384)] unified project version
- [[#5419](https://github.com/seata/seata/pull/5419)] publish images based on java 8/17 and support maven-3.9.0
- [[#5829](https://github.com/seata/seata/pull/5829)] fix codecov chart not display
- [[#5878](https://github.com/seata/seata/pull/5878)] optimize `httpcore` and `httpclient` dependencies
- [[#5917](https://github.com/seata/seata/pull/5917)] upgrade native-lib-loader version
- [[#5926](https://github.com/seata/seata/pull/5926)] optimize some scripts related to Apollo
- [[#5938](https://github.com/seata/seata/pull/5938)] support jmx port in seata
- [[#5951](https://github.com/seata/seata/pull/5951)] remove un support config in jdk17
- [[#5959](https://github.com/seata/seata/pull/5959)] modify code style and remove unused import
- [[#6002](https://github.com/seata/seata/pull/6002)] remove fst serialization
- [[#6045](https://github.com/seata/seata/pull/6045)] optimize derivative product check base on mysql
- [[#6342](https://github.com/seata/seata/pull/6342)] compatible with integration-tx-api module

### security:
- [[#5642](https://github.com/seata/seata/pull/5642)] add Hessian Serializer WhiteDenyList
- [[#5694](https://github.com/seata/seata/pull/5694)] fix several node.js security vulnerabilities
- [[#5801](https://github.com/seata/seata/pull/5801)] fix some dependencies vulnerability
- [[#5805](https://github.com/seata/seata/pull/5805)] fix some serializer vulnerabilities
- [[#5868](https://github.com/seata/seata/pull/5868)] fix npm package vulnerabilities
- [[#5916](https://github.com/seata/seata/pull/5916)] upgrade nodejs dependency
- [[#5942](https://github.com/seata/seata/pull/5942)] upgrade dependencies version
- [[#5987](https://github.com/seata/seata/pull/5987)] upgrade some dependencies version
- [[#6013](https://github.com/seata/seata/pull/6013)] upgrade seata-server spring version

### test:
- [[#5308](https://github.com/seata/seata/pull/5308)] add unit test [FileLoader, ObjectHolder, StringUtils]
- [[#5309](https://github.com/seata/seata/pull/5309)] add unit test [ArrayUtils, ConfigTools, MapUtil]
- [[#5335](https://github.com/seata/seata/pull/5335)] add unit test [EnhancedServiceLoader,ExtensionDefinition,SizeUtilTest,ReflectionUtil,LowerCaseLinkHashMap,FileLoader,ObjectHolder]
- [[#5366](https://github.com/seata/seata/pull/5366)] fix UpdateExecutorTest failed
- [[#5383](https://github.com/seata/seata/pull/5383)] fix multi spring version test failed
- [[#5391](https://github.com/seata/seata/pull/5391)] add unit test for config module
- [[#5428](https://github.com/seata/seata/pull/5428)] fix FileTransactionStoreManagerTest failed
- [[#5622](https://github.com/seata/seata/pull/5622)] add unit test [ExporterType, RegistryType]
- [[#5637](https://github.com/seata/seata/pull/5637)] add unit test [BatchResultMessage, HeartbeatMessage, RegisterRMResponse, ResultCode, RegisterTMResponse, MergeResultMessage, MergedWarpMessage, Version]
- [[#5893](https://github.com/seata/seata/pull/5893)] remove sofa test cases
- [[#5845](https://github.com/seata/seata/pull/5845)] upgrade druid and add `test-druid.yml`
- [[#5863](https://github.com/seata/seata/pull/5863)] fix unit test in java 21
- [[#5986](https://github.com/seata/seata/pull/5986)] fix zookeeper UT failed
- [[#5995](https://github.com/seata/seata/pull/5995)] add test cases for RaftClusterMetadataMsg
- [[#6001](https://github.com/seata/seata/pull/6001)] add test cases for RaftMsgExecute under branch package
- [[#5996](https://github.com/seata/seata/pull/5996)] add test cases for RaftMsgExecute under global package
- [[#6003](https://github.com/seata/seata/pull/6003)] add test cases for RaftMsgExecute under lock package
- [[#6009](https://github.com/seata/seata/pull/6009)] add test cases for RaftServerFactory


### Contributors:

Thanks to these contributors for their code commits. Please report an unintended omission.

- [slievrly](https://github.com/slievrly)
- [xssdpgy](https://github.com/xssdpgy)
- [albumenj](https://github.com/albumenj)
- [PeppaO](https://github.com/PeppaO)
- [yuruixin](https://github.com/yuruixin)
- [CrazyLionLi](https://github.com/JavaLionLi)
- [xingfudeshi](https://github.com/xingfudeshi)
- [Bughue](https://github.com/Bughue)
- [pengten](https://github.com/pengten)
- [wangliang181230](https://github.com/wangliang181230)
- [GoodBoyCoder](https://github.com/GoodBoyCoder)
- [funky-eyes](https://github.com/funky-eyes)
- [isharpever](https://github.com/isharpever)
- [mxsm](https://github.com/mxsm)
- [liuqiufeng](https://github.com/liuqiufeng)
- [l81893521](https://github.com/l81893521)
- [dmego](https://github.com/dmego)
- [zsp419](https://github.com/zsp419)
- [tuwenlin](https://github.com/tuwenlin)
- [sixlei](https://github.com/sixlei)
- [yixia](https://github.com/wt-better)
- [capthua](https://github.com/capthua)
- [robynron](https://github.com/robynron)
- [XQDD](https://github.com/XQDD)
- [Weelerer](https://github.com/Weelerer)
- [Ifdevil](https://github.com/Ifdevil)
- [iquanzhan](https://github.com/iquanzhan)
- [leizhiyuan](https://github.com/leizhiyuan)
- [Aruato](https://github.com/Aruato)
- [ptyin](https://github.com/ptyin)
- [jsbxyyx](https://github.com/jsbxyyx)
- [xxxcrel](https://github.com/xxxcrel)


Also, we receive many valuable issues, questions and advices from our community. Thanks for you all.


#### Link

- **Seata:** https://github.com/seata/seata
- **Seata-Samples:** https://github.com/seata/seata-samples
- **Release:** https://github.com/seata/seata/releases
- **WebSite:** https://seata.io

</details>
