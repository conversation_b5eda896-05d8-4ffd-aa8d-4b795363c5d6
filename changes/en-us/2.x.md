<!--
    Licensed to the Apache Software Foundation (ASF) under one or more
    contributor license agreements.  See the NOTICE file distributed with
    this work for additional information regarding copyright ownership.
    The ASF licenses this file to You under the Apache License, Version 2.0
    (the "License"); you may not use this file except in compliance with
    the License.  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0
    
    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
-->
Add changes here for all PR submitted to the 2.x branch.

<!-- Please add the `changes` to the following location(feature/bugfix/optimize/test) based on the type of PR -->

### feature:

- [[#7261](https://github.com/apache/incubator-seata/pull/7261)] enforce account initialization and disable default credentials


### bugfix:

- [[#7349](https://github.com/apache/incubator-seata/pull/7349)] Resolve NullPointerException in EtcdRegistryServiceImplMockTest
- [[#7354](https://github.com/apache/incubator-seata/pull/7354)] fix the drivers in the libs folder cannot be loaded
- [[#7356](https://github.com/apache/incubator-seata/pull/7356)] fix codecov bug
- [[#7370](https://github.com/apache/incubator-seata/pull/7370)] fix ISSUE_TEMPLATE not work
- [[#7397](https://github.com/apache/incubator-seata/pull/7397)] Resolve NullPointer and port binding errors


### optimize:

- [[#7270](https://github.com/apache/incubator-seata/pull/7270)] enhance ci configuration
- [[#7282](https://github.com/apache/incubator-seata/pull/7282)] optimize unexpected NullPointerException in lookup method in FileRegistryServiceImpl class
- [[#7310](https://github.com/seata/seata/pull/7310)] Optimize minor issues in the naming-server
- [[#7329](https://github.com/apache/incubator-seata/pull/7329)] upgrade tomcat to 9.0.100
- [[#7346](https://github.com/apache/incubator-seata/pull/7346)] replace tomcat with netty-http on the server
- [[#7344](https://github.com/apache/incubator-seata/pull/7344)] raft mode performs transaction size check in advance
- [[#7343](https://github.com/apache/incubator-seata/pull/7343)] upgrade tomcat to 9.0.104
- [[#7337](https://github.com/apache/incubator-seata/pull/7337)] Add ChannelEventListener support to prevent memory leaks
- [[#7344](https://github.com/apache/incubator-seata/pull/7344)] raft mode performs transaction size check in advance
- [[#7345](https://github.com/apache/incubator-seata/pull/7345)] add empty check and duplicate type check to RegistryFactory
- [[#7350](https://github.com/apache/incubator-seata/pull/7350)] optimize codecov.yml
- [[#7360](https://github.com/apache/incubator-seata/pull/7360)] Update resource cleanup logic for channel disconnection
- [[#7363](https://github.com/apache/incubator-seata/pull/7363)] Upgrade npmjs dependencies
- [[#7372](https://github.com/apache/incubator-seata/pull/7372)] optimize license ignore
- [[#7375](https://github.com/apache/incubator-seata/pull/7375)] optimize close() logic of discovery module
- [[#7388](https://github.com/apache/incubator-seata/pull/7388)] optimize binary packaging directory structure
- [[#7412](https://github.com/apache/incubator-seata/pull/7412)] Helm template adapted to the new version of seata
- [[#7414](https://github.com/apache/incubator-seata/pull/7414)] Remove the unused defaultEventExecutorGroup from the NettyClientBootstrap
- [[#7415](https://github.com/apache/incubator-seata/pull/7415)] Use threadPool to asynchronously process server http requests
- [[#7418](https://github.com/apache/incubator-seata/pull/7418)] add jackson notice
- [[#7419](https://github.com/apache/incubator-seata/pull/7419)] Add maven profile to support packaging source code
- [[#7428](https://github.com/apache/incubator-seata/pull/7428)] pmd-check log as ERROR level
- [[#7430](https://github.com/apache/incubator-seata/pull/7430)] Add support for parsing @RequestParam annotation in netty-http-server
- [[#7432](https://github.com/apache/incubator-seata/pull/7432)] conditionally include test modules using Maven profiles
- [[#7445](https://github.com/apache/incubator-seata/pull/7432)] separate the license from the server and namingserver
- [[#7426](https://github.com/apache/incubator-seata/pull/7426)] add some license header


### security:

- [[#PR_NO](https://github.com/seata/seata/pull/PR_NO)] upgrade XXX

### test:

- [[#7092](https://github.com/apache/incubator-seata/pull/7092)] fix the issue of NacosMockTest failing to run
- [[#7098](https://github.com/apache/incubator-seata/pull/7098)] Add unit tests for the `seata-common` module
- [[#7160](https://github.com/apache/incubator-seata/pull/7160)] Refactored tests in `LowerCaseLinkHashMapTest` to use parameterized unit testing
- [[#7167](https://github.com/apache/incubator-seata/pull/7167)] Refactored tests in `DurationUtilTest` to simplify and use parameterized unit testing
- [[#7189](https://github.com/apache/incubator-seata/pull/7189)] fix the runtime exception in the saga test case
- [[#7197](https://github.com/apache/incubator-seata/pull/7197)] add some UT cases for config module
- [[#7199](https://github.com/apache/incubator-seata/pull/7199)] add some UT cases for client processor
- [[#7203](https://github.com/apache/incubator-seata/pull/7203)] Refactored tests in rm.datasource.sql.Druid and seata-sqlparser-druid module
- [[#7221](https://github.com/apache/incubator-seata/pull/7221)] add UT for gRPC Encoder/Decode
- [[#7227](https://github.com/apache/incubator-seata/pull/7227)] add mock test for seata-discovery-consul module
- [[#7233][https://github.com/apache/incubator-seata/pull/7233]] add mock test for seata-discovery-etcd3
- [[#7243](https://github.com/apache/incubator-seata/pull/7243)] add unit test for seata-discovery-eureka
- [[#7255](https://github.com/apache/incubator-seata/pull/7255)] more unit tests for Discovery-Eureka
- [[#7286](https://github.com/apache/incubator-seata/pull/7286)] Simplified complex test testMsgSerialize in RaftSyncMessageTest by separating it into two tests
- [[#7287](https://github.com/apache/incubator-seata/pull/7287)] Refactored testGetErrorMsgWithValidCodeReturnsExpectedMsg to use parameterized unit testing
- [[#7288](https://github.com/apache/incubator-seata/pull/7288)] Refactored testSetCodeAndMsgUpdatesValuesCorrectly to use parameterized unit testing
- [[#7294](https://github.com/apache/incubator-seata/pull/7294)] improved test testGetInsertParamsValue in SqlServerInsertRecognizerTest by splitting and parameterizing
- [[#7295](https://github.com/apache/incubator-seata/pull/7295)] updated 3 tests in StringUtilsTest to use parameterized unit testing
- [[#7205](https://github.com/apache/incubator-seata/issues/7205)] add UT for namingserver module
- [[#7359](https://github.com/apache/incubator-seata/issues/7359)] merge submodule test reports
- [[#7423](https://github.com/apache/incubator-seata/pull/7423)] add UT for org.apache.seata.spring.annotation.scannercheckers
- [[#7420](https://github.com/apache/incubator-seata/pull/7420)] add UT for RemotingFactoryBeanParser class
- [[#7379](https://github.com/apache/incubator-seata/issues/7379)] add UT for TccAnnotationProcessor class
- [[#7422](https://github.com/apache/incubator-seata/pull/7422)] add UT for seata-spring-boot-starter module
- [[#7433](https://github.com/apache/incubator-seata/pull/7433)] add UT for GlobalTransactionScanner class
- [[#7436](https://github.com/apache/incubator-seata/pull/7436)] fix namingserver ut error
- [[#7435](https://github.com/apache/incubator-seata/pull/7435)] Add common test config for dynamic server port assignment in tests
- [[#7442](https://github.com/apache/incubator-seata/pull/7442)] add some UT for saga compatible

### refactor:

- [[#7315](https://github.com/apache/incubator-seata/pull/7315)] Refactor log testing to use ListAppender for more accurate and efficient log capture


### doc:

- [[#PR_NO](https://github.com/seata/seata/pull/PR_NO)] doc XXX


Thanks to these contributors for their code commits. Please report an unintended omission.

<!-- Please make sure your Github ID is in the list below -->

- [slievrly](https://github.com/slievrly)
- [Monilnarang](https://github.com/Monilnarang)
- [xingfudeshi](https://github.com/xingfudeshi)
- [wjwang00](https://github.com/wjwang00)
- [YongGoose](https://github.com/YongGoose)
- [JisoLya](https://github.com/JisoLya)
- [YoWuwuuuw](https://github.com/YoWuwuuuw)
- [PleaseGiveMeTheCoke](https://github.com/PleaseGiveMeTheCoke)
- [funky-eyes](https://github.com/funky-eyes)
- [xucq07](https://github.com/xucq07)
- [PengningYang](https://github.com/PengningYang)
- [WangzJi](https://github.com/WangzJi)
- [maple525866](https://github.com/maple525866)
- [YvCeung](https://github.com/YvCeung)
- [jsbxyyx](https://github.com/jsbxyyx)



Also, we receive many valuable issues, questions and advices from our community. Thanks for you all.
