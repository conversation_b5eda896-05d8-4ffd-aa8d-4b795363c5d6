<!--
    Licensed to the Apache Software Foundation (ASF) under one or more
    contributor license agreements.  See the NOTICE file distributed with
    this work for additional information regarding copyright ownership.
    The ASF licenses this file to You under the Apache License, Version 2.0
    (the "License"); you may not use this file except in compliance with
    the License.  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0
    
    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
-->
所有提交到 2.x 分支的 PR 请在此处登记。

<!-- 请根据PR的类型添加 `变更记录` 到以下对应位置(feature/bugfix/optimize/test) 下 -->

### feature:
- [[#6536](https://github.com/apache/incubator-seata/pull/6536)] 支持 naming server客户端
- [[#6226](https://github.com/apache/incubator-seata/pull/6226)] 支持seata私有协议多版本兼容
- [[#6537](https://github.com/apache/incubator-seata/pull/6537)] 支持 Namingserver
- [[#6538](https://github.com/apache/incubator-seata/pull/6538)] seata server端集成naming server
- [[#6766](https://github.com/apache/incubator-seata/pull/6766)] 添加tcc三阶段钩子函数，方便用户拓展业务逻辑（比如多数据源情况下可以用于切换数据源）

### bugfix:
- [[#6592](https://github.com/apache/incubator-seata/pull/6592)] fix @Async注解ClusterWatcherManager中不生效的问题
- [[#6624](https://github.com/apache/incubator-seata/pull/6624)] 修复 Alibaba Dubbo 转换错误
- [[#6627](https://github.com/apache/incubator-seata/pull/6627)] 修复 xaEnded 没有重置
- [[#6626](https://github.com/apache/incubator-seata/pull/6626)] 修复 hsf ConsumerModel 转换错误
- [[#6816](https://github.com/apache/incubator-seata/pull/6816)] 修复在非全局事务上下文中获取branchType时的NPE问题
- [[#6640](https://github.com/apache/incubator-seata/pull/6640)] 优化codecov相关配置
- [[#6642](https://github.com/apache/incubator-seata/pull/6642)] 修复codecov token找不到导致无法提交单测覆盖度报告
- [[#6661](https://github.com/apache/incubator-seata/pull/6661)] 修复`tableMeta`缓存定时刷新失效问题
- [[#6486](https://github.com/apache/incubator-seata/pull/6486)] 修复在mysql数据库下undo_log sql数据超过最大包大小错误
- [[#6668](https://github.com/apache/incubator-seata/pull/6668)] 解决namingserver同一个集群下instance添加和删除时的线程安全问题
- [[#6678](https://github.com/apache/incubator-seata/pull/6678)] 修复由于表名大小写问题导致的相同记录生成不同RowKey的问题
- [[#6697](https://github.com/apache/incubator-seata/pull/6697)] v0版本的ByteBuf不应由父类先解码
- [[#6707](https://github.com/apache/incubator-seata/pull/6707)] 修复Oracle XA事务中只读分支提交出错的问题
- [[#6711](https://github.com/apache/incubator-seata/pull/6711)] 修复达梦数据库的getRollbackInfo没有解压缩的问题
- [[#6714](https://github.com/apache/incubator-seata/pull/6714)] 修复达梦数据库的delete sql回滚失败的问题
- [[#6511](https://github.com/apache/incubator-seata/pull/6511)] 修复在使用SQLServer时，AT模式delete语句无法正常回滚的问题
- [[#6701](https://github.com/apache/incubator-seata/pull/6728)] 修复达梦数据库的对dm.jdbc.driver.DmdbTimestamp的支持
- [[#6757](https://github.com/apache/incubator-seata/pull/6757)] 修复client通过namingserver只能获取到一个tc节点的bug
- [[#6769](https://github.com/apache/incubator-seata/pull/6769)] 修复tcc fence死锁
- [[#6778](https://github.com/apache/incubator-seata/pull/6778)] 修复namingserver的节点term为0问题
- [[#6765](https://github.com/apache/incubator-seata/pull/6765)] 改进MySQL驱动加载机制，将自定义类加载器替换为系统类加载器，更兼容简化流程
- [[#6781](https://github.com/apache/incubator-seata/pull/6781)] 修复tc下线时,由于定时任务没有先关闭,导致下线后还会被注册上,需要靠namingserver的健康检查来下线的bug
- [[#6785](https://github.com/apache/incubator-seata/pull/6785)] 修复 prometheus 在与 Nacos 集成时无法返回 seata metrics 数据的问题
- [[#6797](https://github.com/apache/incubator-seata/pull/6797)] 当查询的集群地址为空时，获取可用的任意集群地址
- [[#6800](https://github.com/apache/incubator-seata/pull/6800)] 使异常消息对所有数据库驱动程序通用
- [[#6812](https://github.com/apache/incubator-seata/pull/6812)] 修复切换事务分组和节点下线时namingserver没有实时感知和推送的bug
- [[#6759](https://github.com/apache/incubator-seata/pull/6759)] 修复跨库表主动刷新`tableMeta`的异常问题
- [[#6817](https://github.com/apache/incubator-seata/pull/6817)] 修复namingserver切换事务分组失效的问题
- [[#6820](https://github.com/apache/incubator-seata/pull/6820)] 修复Dockerfile得文件结构错误
- [[#6825](https://github.com/apache/incubator-seata/pull/6825)] 修复Postgres的XA模式事务超时无法回滚问题
- [[#6833](https://github.com/apache/incubator-seata/pull/6833)] 插入全局锁时 SQLIntegrityConstraintViolationException 捕获不正确
- [[#6835](https://github.com/apache/incubator-seata/pull/6835)] 修复HttpClientUtil中post方法请求体缺失的问题
- [[#6845](https://github.com/apache/incubator-seata/pull/6845)] 修复rocksdb open相同文件多次的问题
- [[#6840](https://github.com/apache/incubator-seata/pull/6840)] 修复ProcessorYaml中不安全的反序列化
- [[#6843](https://github.com/apache/incubator-seata/pull/6843)] 修复从控制台发送POST请求时出现的403错误
- [[#6850](https://github.com/apache/incubator-seata/pull/6850)] raft模式向下兼容2.0版本
- [[#6855](https://github.com/apache/incubator-seata/pull/6855)] 修复raft缩容后元数据中残留该节点的问题(需先升级到2.2再进行缩容)
- [[#6859](https://github.com/apache/incubator-seata/pull/6859)] 移除重复的依赖

### optimize:
- [[#6499](https://github.com/apache/incubator-seata/pull/6499)] 拆分 committing 和 rollbacking 状态的任务线程池
- [[#6208](https://github.com/apache/incubator-seata/pull/6208)] 支持多版本的Seata序列化
- [[#6209](https://github.com/apache/incubator-seata/pull/6209)] 解开 RpcMessage 和 Encoder/Decoder 的互相依赖
- [[#6634](https://github.com/apache/incubator-seata/pull/6634)] 根据协议版本指定channel handle
- [[#6523](https://github.com/apache/incubator-seata/pull/6523)] 升级 alibaba/druid 的版本到1.2.20
- [[#6566](https://github.com/apache/incubator-seata/pull/6566)] 支持GlobalTransactionScanner类中exposeProxy属性的配置
- [[#6534](https://github.com/apache/incubator-seata/pull/6534)] 优化: 发送异步响应
- [[#6534](https://github.com/apache/incubator-seata/pull/6648)] 增加license header信息
- [[#6666](https://github.com/apache/incubator-seata/pull/6666)] 添加ExceptionUtil工具类用于解包装异常
- [[#6654](https://github.com/apache/incubator-seata/pull/6654)] 增加Namingserver打包功能
- [[#6667](https://github.com/apache/incubator-seata/pull/6667)] 优化Namingserver日志输出
- [[#6687](https://github.com/apache/incubator-seata/pull/6687)] 删除前端构建的静态代码
- [[#6700](https://github.com/apache/incubator-seata/pull/6700)] 去掉sdk版本检查
- [[#6727](https://github.com/apache/incubator-seata/pull/6727)] 反序列化性能优化
- [[#6732](https://github.com/apache/incubator-seata/pull/6732)] 为application.example.yml与application.raft.example.yml添加默认安全配置 
- [[#6651](https://github.com/apache/incubator-seata/pull/6651)] 为 proto 文件添加 license header
- [[#6653](https://github.com/apache/incubator-seata/pull/6653)] 优化多 license和移除license url
- [[#6655](https://github.com/apache/incubator-seata/pull/6655)] 更新前端 license
- [[#6652](https://github.com/apache/incubator-seata/pull/6652)] 为spring 配置文件添加 license header
- [[#6656](https://github.com/apache/incubator-seata/pull/6656)] 更新源码中的 license
- [[#6650](https://github.com/apache/incubator-seata/pull/6650)] 为 SPI配置文件添加 license header
- [[#6741](https://github.com/apache/incubator-seata/pull/6741)] 升级 tomcat-embed-core 至 9.0.90 版本
- [[#6742](https://github.com/apache/incubator-seata/pull/6742)] 升级 console 模块 npmjs 版本
- [[#6732](https://github.com/apache/incubator-seata/pull/6732)] 为application.example.yml与application.raft.example.yml添加默认安全配置
- [[#6743](https://github.com/apache/incubator-seata/pull/6743)] 升级saga模块npmjs版本
- [[#6746](https://github.com/apache/incubator-seata/pull/6746)] 优化 compatible 模块依赖
- [[#6745](https://github.com/apache/incubator-seata/pull/6745)] 修复 node-gyp 在 arm64 和 macos 构建失败问题
- [[#6749](https://github.com/apache/incubator-seata/pull/6749)] 优化 WebSecurityConfig csrf 处理
- [[#6748](https://github.com/apache/incubator-seata/pull/6748)] 优化 ConsistentHashLoadBalance 算法
- [[#6747](https://github.com/apache/incubator-seata/pull/6747)] 优化 fastjson 反序列化
- [[#6755](https://github.com/apache/incubator-seata/pull/6755)] 优化namingserver代码逻辑
- [[#6763](https://github.com/apache/incubator-seata/pull/6763)] 优化 NacosConfiguration 单例加载
- [[#6761](https://github.com/apache/incubator-seata/pull/6761)] 提升namingserver manager代码可读性
- [[#6768](https://github.com/apache/incubator-seata/pull/6768)] 上报tcc fence事务隔离级别
- [[#6770](https://github.com/apache/incubator-seata/pull/6770)] 通过caffeine map支持namingserver事务分组的过期删除
- [[#6780](https://github.com/apache/incubator-seata/pull/6780)] 优化类 `SerializerServiceLoader` 中的反射操作
- [[#6784](https://github.com/apache/incubator-seata/pull/6784)] 升级 axios 至 1.7.4 版本
- [[#6787](https://github.com/apache/incubator-seata/pull/6787)] 升级 elliptic 至 6.5.7 版本
- [[#6783](https://github.com/apache/incubator-seata/pull/6783)] 将server事务分组修改接口改为/vgroup/v1
- [[#6793](https://github.com/apache/incubator-seata/pull/6793)] 修复 npmjs 依赖冲突问题
- [[#6794](https://github.com/apache/incubator-seata/pull/6794)] 优化 NacosMockTest 单测问题
- [[#6793](https://github.com/apache/incubator-seata/pull/6795)] 独立server的meta信息初始化逻辑
- [[#6806](https://github.com/apache/incubator-seata/pull/6806)] 优化`tableMeta`缓存定时刷新问题
- [[#6808](https://github.com/apache/incubator-seata/pull/6808)] 修改版本号为2.2.0-SNAPSHOT
- [[#6819](https://github.com/apache/incubator-seata/pull/6819)] namingserver与server的合并打包
- [[#6827](https://github.com/apache/incubator-seata/pull/6827)] 重命名namingserver注册类型改为seata
- [[#6836](https://github.com/apache/incubator-seata/pull/6836)] 为CI流程增加独立nacos
- [[#6841](https://github.com/apache/incubator-seata/pull/6841)] 更新license和notice文件并统一依赖版本
- [[#6823](https://github.com/apache/incubator-seata/pull/6823)] 修正DefaultGlobalTransaction日志的错别字
- [[#6779](https://github.com/apache/incubator-seata/pull/6779)] 在config模块中使用curator替代zkclient
- [[#6831](https://github.com/apache/incubator-seata/pull/6831)] 在registry模块中使用curator替代zkclient
- [[#6803](https://github.com/apache/incubator-seata/pull/6803)] 优化 ARM64 架构的编译打包
- [[#6852](https://github.com/apache/incubator-seata/pull/6852)] 优化raft接口
- [[#6863](https://github.com/apache/incubator-seata/pull/6863)] 更新notice.md


### refactor:


### security:

### test:
- [[#6533](https://github.com/apache/incubator-seata/pull/6533)] 增加 Integration-TX-API 模块单元测试覆盖范围
- [[#6608](https://github.com/apache/incubator-seata/pull/6608)] 添加sql-parser-core模块测试用例
- [[#6647](https://github.com/apache/incubator-seata/pull/6647)] 增加saga模块的测试用例覆盖率
- [[#6695](https://github.com/apache/incubator-seata/pull/6695)] 多版本协议的旧版本（< 0.7.1）客户端测试用例
- [[#6752](https://github.com/apache/incubator-seata/pull/6752)] 增加metrics模块测试用例覆盖率
- [[#6764](https://github.com/apache/incubator-seata/pull/6764)] 增加 Apollo Mock 测试用例
- [[#6750](https://github.com/apache/incubator-seata/pull/6750)] 提升spring autoconfigure模块单测覆盖率
- [[#6773](https://github.com/apache/incubator-seata/pull/6773)] 修复codecov图标显示错误的代码覆盖率
- [[#6821](https://github.com/apache/incubator-seata/pull/6821)] 修复单元测试断言


非常感谢以下 contributors 的代码贡献。若有无意遗漏，请报告。

<!-- 请确保您的 GitHub ID 在以下列表中 -->
- [slievrly](https://github.com/slievrly)
- [tuwenlin](https://github.com/tuwenlin)
- [YeonCheolGit](https://github.com/YeonCheolGit)
- [liuqiufeng](https://github.com/liuqiufeng)
- [God-Gan](https://github.com/God-Gan)
- [Bughue](https://github.com/Bughue)
- [funky-eyes](https://github.com/funky-eyes)
- [tanyaofei](https://github.com/tanyaofei)
- [traitsisgiorgos](https://github.com/traitsisgiorgos)
- [wanghongzhou](https://github.com/wanghongzhou)
- [ggbocoder](https://github.com/ggbocoder)
- [azatyamanaev](https://github.com/azatyamanaev)
- [xjlgod](https://github.com/xjlgod)
- [xingfudeshi](https://github.com/xingfudeshi)
- [wuwen5](https://github.com/wuwen5)
- [jsbxyyx](https://github.com/jsbxyyx)
- [iAmClever](https://github.com/iAmClever)
- [GoodBoyCoder](https://github.com/GoodBoyCoder)
- [liuqiufeng](https://github.com/liuqiufeng)
- [caohdgege](https://github.com/caohdgege)
- [TakeActionNow2019](https://github.com/TakeActionNow2019)
- [imashimaro](https://github.com/hmj776521114)
- [lyl2008dsg](https://github.com/lyl2008dsg)
- [lightClouds917](https://github.com/lightClouds917)
- [l81893521](https://github.com/l81893521)
- [laywin](https://github.com/laywin)
- [xiaoxiangyeyu0](https://github.com/xiaoxiangyeyu0)
- [LegGasai](https://github.com/LegGasai)
- [yangli-stu](https://github.com/yangli-stu)
- [heliang666s](https://github.com/heliang666s)



同时，我们收到了社区反馈的很多有价值的issue和建议，非常感谢大家。

