<!--
    Licensed to the Apache Software Foundation (ASF) under one or more
    contributor license agreements.  See the NOTICE file distributed with
    this work for additional information regarding copyright ownership.
    The ASF licenses this file to You under the Apache License, Version 2.0
    (the "License"); you may not use this file except in compliance with
    the License.  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0
    
    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
-->
所有提交到 develop 分支的 PR 请在此处登记。

<!-- 请根据PR的类型添加 `变更记录` 到以下对应位置(feature/bugfix/optimize/test) 下 -->

### feature:
- [[#5803](https://github.com/seata/seata/pull/5803)] docker镜像支持注入JVM参数到容器

### bugfix:
- [[#5749](https://github.com/seata/seata/pull/5749)] 修复在某些情况下，业务sql中主键字段名大小写与表元数据中的不一致，导致回滚失败
- [[#5762](https://github.com/seata/seata/pull/5762)] 修复TableMetaCache的一些字段类型，避免溢出
- [[#5769](https://github.com/seata/seata/pull/5769)] 修复不满足 sofa-rpc 中 setAttachment 方法的参数前缀要求问题
- [[#5814](https://github.com/seata/seata/pull/5814)] 修复druid依赖冲突导致的XA事务开始异常与回滚失败
- [[#5771](https://github.com/seata/seata/pull/5771)] 修复insert executor对关键字未转义的问题
- [[#5819](https://github.com/seata/seata/pull/5814)] 修复oracle alias 解析异常

### optimize:
- [[#5804](https://github.com/seata/seata/pull/5804)] 优化docker镜像的默认时区
- [[#5815](https://github.com/seata/seata/pull/5815)] 支持 Nacos applicationName 属性
- [[#5820](https://github.com/seata/seata/pull/5820)] 统一日志输出目录
- [[#5822](https://github.com/seata/seata/pull/5822)] 升级过时的github actions
- [[#5168](https://github.com/seata/seata/pull/5168)] 发布基于多个java版本的docker镜像

### security:
- [[#5728](https://github.com/seata/seata/pull/5728)] 修复Java依赖漏洞
- [[#5766](https://github.com/seata/seata/pull/5766)] 修复序列化漏洞

### test:
- [[#XXX](https://github.com/seata/seata/pull/XXX)] XXX

非常感谢以下 contributors 的代码贡献。若有无意遗漏，请报告。

<!-- 请确保您的 GitHub ID 在以下列表中 -->
- [slievrly](https://github.com/slievrly)
- [capthua](https://github.com/capthua)
- [robynron](https://github.com/robynron)
- [dmego](https://github.com/dmego)
- [xingfudeshi](https://github.com/xingfudeshi)
- [hadoop835](https://github.com/hadoop835)
- [funky-eyes](https://github.com/funky-eyes)
- [DroidEye2ONGU](https://github.com/DroidEye2ONGU)

同时，我们收到了社区反馈的很多有价值的issue和建议，非常感谢大家。
