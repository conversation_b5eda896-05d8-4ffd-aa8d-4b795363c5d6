<!--
    Licensed to the Apache Software Foundation (ASF) under one or more
    contributor license agreements.  See the NOTICE file distributed with
    this work for additional information regarding copyright ownership.
    The ASF licenses this file to You under the Apache License, Version 2.0
    (the "License"); you may not use this file except in compliance with
    the License.  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0
    
    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
-->
### 1.5.0 

 [source](https://github.com/seata/seata/archive/v1.5.0.zip) |
 [binary](https://github.com/seata/seata/releases/download/v1.5.0/seata-server-1.5.0.zip) 

<details>
  <summary><mark>Release notes</mark></summary>


  ### Seata 1.5.0 

Seata 1.5.0  发布。

Seata 是一款开源的分布式事务解决方案，提供高性能和简单易用的分布式事务服务。

此版本更新如下：


### feature：
  - [[#4115](https://github.com/seata/seata/pull/4115)] 支持用户控制台
  - [[#3472](https://github.com/seata/seata/pull/3472)] 添加redisLocker的lua模式
  - [[#3575](https://github.com/seata/seata/pull/3575)] 支持对锁和会话不同存储的混合使用
  - [[#3374](https://github.com/seata/seata/pull/3374)] 支持mysql INSERT ON DUPLICATE KEY UPDATE
  - [[#3642](https://github.com/seata/seata/pull/3642)] TCC模式支持使用API的形式进行二阶段参数传递
  - [[#3064](https://github.com/seata/seata/pull/3064)] 支持可配置GlobalTransactionInterceptor和TccActionInterceptor的order值
  - [[#2852](https://github.com/seata/seata/pull/2852)] 支持自定义`GlobalTransactionScanner`的扫描对象。
  - [[#3683](https://github.com/seata/seata/pull/3683)] 支持redis分布式锁来避免多tc竞争执行任务
  - [[#3545](https://github.com/seata/seata/pull/3545)] TCC模式支持幂等控制、防悬挂和空回滚
  - [[#3009](https://github.com/seata/seata/pull/3009)] 支持server端以springboot的方式的启动
  - [[#3652](https://github.com/seata/seata/pull/3652)] 支持APM SkyWalking监控。
  - [[#3823](https://github.com/seata/seata/pull/3823)] TCC模式二阶段方法参数列表支持自定义
  - [[#3642](https://github.com/seata/seata/pull/3642)] TCC模式一阶段支持BusinessActionContext隐式传递
  - [[#3856](https://github.com/seata/seata/pull/3856)] 支持 edas-hsf RPC 框架
  - [[#3880](https://github.com/seata/seata/pull/3880)] 贡献文档增加中文版本
  - [[#3869](https://github.com/seata/seata/pull/3869)] 支持从环境 ENV 获取配置
  - [[#2568](https://github.com/seata/seata/pull/2568)] 支持GlobalTransactionInterceptor配置切面表达式
  - [[#3886](https://github.com/seata/seata/pull/3886)] 支持注册中心注册ip的网络偏好设置
  - [[#3906](https://github.com/seata/seata/pull/3906)] 支持 SPI 卸载
  - [[#3668](https://github.com/seata/seata/pull/3668)] 支持kotlin协程
  - [[#3968](https://github.com/seata/seata/pull/3968)] 支持 brpc-java RPC框架
  - [[#4134](https://github.com/seata/seata/pull/4134)] 初始化控制台基础代码
  - [[#4268](https://github.com/seata/seata/pull/4268)] 控制台Global Session页面File模式实现
  - [[#4281](https://github.com/seata/seata/pull/4281)] 控制台Global Session页面和Global LockRedis模式实现
  - [[#4293](https://github.com/seata/seata/pull/4293)] 控制台Global Lock页面File模式实现
  - [[#4335](https://github.com/seata/seata/pull/4335)] 实现配置中心上传配置交互脚本(nacos,etcd3)
  - [[#4360](https://github.com/seata/seata/pull/4360)] 实现配置中心上传配置交互脚本(apollo,consul,zk)
  - [[#4320](https://github.com/seata/seata/pull/4320)] 实现控制台db模式全局事务、锁查询接口
  - [[#4435](https://github.com/seata/seata/pull/4435)] 控制台前端页面实现
  - [[#4480](https://github.com/seata/seata/pull/4480)] 实现DefaultAuthSigner的默认签名加密方法
  - [[#3870](https://github.com/seata/seata/pull/3870)] 让seata-bom成为真正的Bill-Of-Material
  - [[#3487](https://github.com/seata/seata/pull/3487)] 添加分布式锁的db实现
  - [[#3889](https://github.com/seata/seata/pull/3889)] 注册中心添加心跳
  - [[#3951](https://github.com/seata/seata/pull/3951)] 支持zstd压缩 
  - [[#2838](https://github.com/seata/seata/pull/2838)] Saga 支持springboot项目的自动配置

### bugfix：
  - [[#3497](https://github.com/seata/seata/pull/3497)] 修复TCC模式并发量较大时线程池导致的超时问题
  - [[#3686](https://github.com/seata/seata/pull/3686)] 修复Apollo集群配置项错误及NPE错误
  - [[#3702](https://github.com/seata/seata/pull/3702)] 修改注释
  - [[#3716](https://github.com/seata/seata/pull/3716)] 修复findTargetClass方法的错误
  - [[#3717](https://github.com/seata/seata/pull/3717)] 更正interval的拼写
  - [[#3773](https://github.com/seata/seata/pull/3773)] 修复consul注册中心在自定义集群名下无法获取TC集群
  - [[#3695](https://github.com/seata/seata/pull/3695)] 修复mariadb无法创建XA连接的问题
  - [[#3783](https://github.com/seata/seata/pull/3783)] 修复store mode不生效问题
  - [[#3740](https://github.com/seata/seata/pull/3740)] 修复在某些情况下，当`Saga`事务结束时`LocalThread`未被清除的问题
  - [[#3792](https://github.com/seata/seata/pull/3792)] 修复Server 无法获取 redis host的问题
  - [[#3828](https://github.com/seata/seata/pull/3828)] 修复StringUtils抛出StackOverflowError的问题
  - [[#3817](https://github.com/seata/seata/pull/3817)] 修复TC在SkyWalking拓扑图节点不汇聚的问题
  - [[#3803](https://github.com/seata/seata/pull/3803)] 修复 ReflectionUtil 抛出不预期异常问题
  - [[#3879](https://github.com/seata/seata/pull/3879)] 修复 posrgresql多schema无法找到channel问题
  - [[#3881](https://github.com/seata/seata/pull/3881)] 修复不存在的相同 DataId 不同默认值返回相同值的问题
  - [[#3897](https://github.com/seata/seata/pull/3897)] 修复FastjsonUndoLogParser中 localdatatime类型不能回滚的问题
  - [[#3901](https://github.com/seata/seata/pull/3901)] 修复 seataio/seata-server 中 servlet-api 冲突无法启动问题
  - [[#3931](https://github.com/seata/seata/pull/3931)] 修复 线程池拒绝执行情况下,dump内存文件名和路径错误的问题
  - [[#3949](https://github.com/seata/seata/pull/3949)] 修复`nacos-config.py`不会跳过空白选项的问题，解决多个分割选项可能导致内容丢失的问题
  - [[#3988](https://github.com/seata/seata/pull/3988)] 修复 nacos 的密码带有特殊字符导致用户名不存在问题
  - [[#3978](https://github.com/seata/seata/pull/3978)] 修复 future timeout 引发的 NPE 问题
  - [[#3998](https://github.com/seata/seata/pull/3998)] 修复 jedis multi.exec 的 NPE 问题
  - [[#4011](https://github.com/seata/seata/pull/4011)] 修复 springboot下无法获取distributed-lock-table配置
  - [[#4025](https://github.com/seata/seata/pull/4025)] 修复潜在的数据库资源泄露
  - [[#4023](https://github.com/seata/seata/pull/4023)] 修复 dubbo部分场景存在xid未清除的问题
  - [[#4032](https://github.com/seata/seata/pull/4032)] 修复server端的ShutdownHook在资源释放时，ApplicationContext已关闭的问题
  - [[#4039](https://github.com/seata/seata/pull/4039)] 修复 本地事务抛出异常之后，RM没有清除xid
  - [[#4074](https://github.com/seata/seata/pull/4074)] 修复XA模式资源悬挂问题
  - [[#4107](https://github.com/seata/seata/pull/4107)] 修复项目构建时的死锁问题
  - [[#4158](https://github.com/seata/seata/pull/4158)] 修复logback无法加载到`RPC_PORT`的问题
  - [[#4162](https://github.com/seata/seata/pull/4162)] 修复Redis注册中心内置配置名导致启动报错问题
  - [[#4165](https://github.com/seata/seata/pull/4165)] 修复 `StringUtils.toString(obj)` 当obj是基本数据数组时，抛出`ClassCastException`的问题
  - [[#4169](https://github.com/seata/seata/pull/4169)] 修复xa模式originalConnection已关闭，导致二阶段无法执行
  - [[#4177](https://github.com/seata/seata/pull/4177)] 修复当事务超时且刚好tm发起commit决议时,意外造成全局锁释放的问题
  - [[#4174](https://github.com/seata/seata/pull/4174)] 修复删除 undolog 时连接关闭问题
  - [[#4189](https://github.com/seata/seata/pull/4189)] 修复 `kafka-appender.xml` 和 `logstash-appender.xml` 两个配置文件中`${}`表达式中的默认值前少了横杆的问题。
  - [[#4213](https://github.com/seata/seata/pull/4213)] 修复部分"sessionMode"代码没执行导致启动失败问题
  - [[#4220](https://github.com/seata/seata/pull/4220)] 修复 `zstd-compressor` 模块未合并到 `seata-all` 中的问题，同时修正其包名。另外，补充了 `kotlin-maven-plugin` 的版本号；顺便优化打包配置。
  - [[#4222](https://github.com/seata/seata/pull/4222)] 修复字段列表为空时，插入语句无法回滚的问题
  - [[#4253](https://github.com/seata/seata/pull/4253)] UpdateExecutor存储被真实修改的字段，而不是只存储set子句里面的字段
  - [[#4233](https://github.com/seata/seata/pull/4233)] 修复 lock 和 branch 数据残留问题
  - [[#4276](https://github.com/seata/seata/pull/4276)] 修复 seata-test 单测不运行的问题
  - [[#4278](https://github.com/seata/seata/pull/4278)] 修复mysql的Blob/Clob/NClob数据类型无法反序列化的问题
  - [[#4302](https://github.com/seata/seata/pull/4302)] 修复其他ORM可能存在获取不到自增主键值的问题
  - [[#4308](https://github.com/seata/seata/pull/4308)] 修复Postgresql多个schema下存在相同表的TableMetaCache解析问题
  - [[#4326](https://github.com/seata/seata/pull/4326)] 修复使用 mariadb 驱动程序时无法构建 Executor 的问题
  - [[#4355](https://github.com/seata/seata/pull/4355)] 修复使用 mysql Loadbalance模式resourceId被误判为resourceIds的问题
  - [[#4310](https://github.com/seata/seata/pull/4310)] 修复通过"SELECT LAST_INSERT_ID"获取mysql数据库自增id失败的问题
  - [[#4331](https://github.com/seata/seata/pull/4331)] 修复使用ONLY_CARE_UPDATE_COLUMNS配置可能出现的脏写校验异常
  - [[#4408](https://github.com/seata/seata/pull/4408)] 修复容器环境中设置环境变量无效的问题
  - [[#4441](https://github.com/seata/seata/pull/4441)] 修复redis模式下查询时未关闭Pipeline和分支注册后添加分支session时branchSessions为null的问题
  - [[#4438](https://github.com/seata/seata/pull/4438)] 修复develop版本file模式下GlobalSession在延迟删除的情况下无法被正常删除的问题
  - [[#4432](https://github.com/seata/seata/pull/4432)] 修复develop版本下ServerApplicationListener无法读取配置中心配置的问题
  - [[#4452](https://github.com/seata/seata/pull/4452)] 修复'service.disableGlobalTransaction'配置的日志输出错误
  - [[#4449](https://github.com/seata/seata/pull/4449)] 修复redis分页查询npe问题,优化readession限制查询条数后均衡返回结果
  - [[#4459](https://github.com/seata/seata/pull/4459)] 修复develop版本下oracle和pgsql数据库生成前后镜像失败的问题
  - [[#4471](https://github.com/seata/seata/pull/4471)] 修复develop分支下，运行时切换事务分组对应集群引起的错误
  - [[#4474](https://github.com/seata/seata/pull/4474)] 修复Mysql多位Bit类型字段回滚错误
  - [[#4492](https://github.com/seata/seata/pull/4492)] 修复develop分支下eureka注册中心无法动态更新服务列表的问题
  - [[#4228](https://github.com/seata/seata/pull/4228)] 修复tc获取不同ip的rm连接导致的xa模式资源悬挂问题
  - [[#4535](https://github.com/seata/seata/pull/4535)] 修复FileSessionManagerTest单测错误
  - [[#4561](https://github.com/seata/seata/pull/4561)] 修复 allSessions/findGlobalSessions 某些情况下返回null
  - [[#4505](https://github.com/seata/seata/pull/4505)] 修复time类型的fastjson序列化问题
  - [[#4579](https://github.com/seata/seata/pull/4579)] 修复MySQLInsertOrUpdateExecutor的prepareUndoLogAll
  - [[#4005](https://github.com/seata/seata/pull/4005)] 修复PK约束名称与属于PK的唯一索引名称不同
  - [[#4062](https://github.com/seata/seata/pull/4062)] 修复saga复杂参数序列化问题
  - [[#4199](https://github.com/seata/seata/pull/4199)] 修复rpc tm 请求超时
  - [[#4352](https://github.com/seata/seata/pull/4352)] 修复sql解析器的一些问题
  - [[#4487](https://github.com/seata/seata/pull/4487)] 移除Pagination hideOnlyOnePage 属性
  - [[#4449](https://github.com/seata/seata/pull/4449)] 优化redis limit并修复redis分页bug
  - [[#4608](https://github.com/seata/seata/pull/4608)] 修复测试用例
  - [[#3110](https://github.com/seata/seata/pull/3110)] 修复单元测试的一些问题


### optimize：
  - [[#4163](https://github.com/seata/seata/pull/4163)] 完善开发者奉献文档
  - [[#3678](https://github.com/seata/seata/pull/3678)] 补充遗漏的配置及新版本pr登记md文件
  - [[#3654](https://github.com/seata/seata/pull/3654)] 修正拼写，applicationContex -> applicationContext
  - [[#3615](https://github.com/seata/seata/pull/3615)] 二阶段同步提交时,全局事务记录异步删除
  - [[#3687](https://github.com/seata/seata/pull/3687)] 修复某些场景下无法重试全局锁的问题
  - [[#3689](https://github.com/seata/seata/pull/3689)] 修正script/server/config/file.properties中属性编写错误
  - [[#3700](https://github.com/seata/seata/pull/3700)] 优化buildLockKey方法的效率
  - [[#3588](https://github.com/seata/seata/pull/3588)] 优化数据源自动代理的流程
  - [[#3528](https://github.com/seata/seata/pull/3528)] 优化redis模式内存占用
  - [[#3626](https://github.com/seata/seata/pull/3626)] 移除重复的change status代码
  - [[#3722](https://github.com/seata/seata/pull/3722)] 添加分布式锁的基础代码
  - [[#3713](https://github.com/seata/seata/pull/3713)] 统一enableClientBatchSendRequest的默认值
  - [[#3120](https://github.com/seata/seata/pull/3120)] 优化`Configuration`的部分代码，并添加单元测试
  - [[#3735](https://github.com/seata/seata/pull/3735)] 当TC只有单个节点时，不进行非必要的负载均衡操作
  - [[#3770](https://github.com/seata/seata/pull/3770)] 关闭一些未关闭的对象
  - [[#3627](https://github.com/seata/seata/pull/3627)] 使用TreeMap替换TableMeta中的LinkedHashMap以兼容高版本的MySQL
  - [[#3760](https://github.com/seata/seata/pull/3760)] 优化`seata-server`的logback相关的配置
  - [[#3765](https://github.com/seata/seata/pull/3765)] 将添加配置类的操作从`AutoConfiguration`转移到`EnvironmentPostProcessor`中，提升该操作的优先级
  - [[#3730](https://github.com/seata/seata/pull/3730)] 重构TCC模式相关的代码，方便以后做功能扩展
  - [[#3820](https://github.com/seata/seata/pull/3820)] 在表`tcc_fence_log`中添加字段`action_name`，用于查看该条记录是由哪个action产生的 
  - [[#3738](https://github.com/seata/seata/pull/3738)] `JacksonUndoLogParser`支持解析`LocalDateTime`(支持微秒时间)
  - [[#3794](https://github.com/seata/seata/pull/3794)] 优化`seata-server`的打包配置，修正Dockerfile的错误配置，并将Dockerfile也打包进去
  - [[#3795](https://github.com/seata/seata/pull/3795)] 优化`zkRegistry`lookup方法性能
  - [[#3840](https://github.com/seata/seata/pull/3840)] 优化`apm-skwalking`操作方法生成规则
  - [[#3834](https://github.com/seata/seata/pull/3834)] 优化`seata-distribution`增加apm-seata-skywalking包
  - [[#3847](https://github.com/seata/seata/pull/3847)] 优化ConcurrentHashMap.newKeySet替换ConcurrentSet
  - [[#3849](https://github.com/seata/seata/pull/3849)] 优化字符串拼接
  - [[#3890](https://github.com/seata/seata/pull/3890)] 优化insert后镜像仅查询插入字段
  - [[#3895](https://github.com/seata/seata/pull/3895)] 优化解码异常
  - [[#3212](https://github.com/seata/seata/pull/3212)] 优化解析OrderBy，Limit条件代码结构
  - [[#3898](https://github.com/seata/seata/pull/3898)] 增加docker maven 插件
  - [[#3904](https://github.com/seata/seata/pull/3904)] 增强 metrics 和修复 seata-server 单测不运行的问题
  - [[#3905](https://github.com/seata/seata/pull/3905)] 优化 nacos-config.sh 支持 ash
  - [[#3935](https://github.com/seata/seata/pull/3935)] 优化以redis为注册中心时,发送多条命令使用pipeline
  - [[#3916](https://github.com/seata/seata/pull/3916)] 优化注册中心服务节点列表地址探活
  - [[#3918](https://github.com/seata/seata/pull/3918)] 缓存Field和Method的反射结果
  - [[#3311](https://github.com/seata/seata/pull/3311)] 支持从consul单一key中读取所有配置
  - [[#3907](https://github.com/seata/seata/pull/3907)] 优化设置 Server 端口
  - [[#3912](https://github.com/seata/seata/pull/3912)] 支持通过env配置JVM参数
  - [[#3939](https://github.com/seata/seata/pull/3939)] 使用map优化大量的判断代码
  - [[#3955](https://github.com/seata/seata/pull/3955)] 添加启动banner
  - [[#4266](https://github.com/seata/seata/pull/4266)] 修改由于修改记录过多导致分支注册及lock释放失败的问题 
  - [[#3949](https://github.com/seata/seata/pull/3949)] `nacos-config.py` 支持默认参数和选择性输入参数
  - [[#3954](https://github.com/seata/seata/pull/3954)] 移除对druid依赖中过期方法的调用
  - [[#3981](https://github.com/seata/seata/pull/3981)] 优化服务端口的优先级设置
  - [[#4013](https://github.com/seata/seata/pull/4013)] 优化可用TC地址检测
  - [[#3982](https://github.com/seata/seata/pull/3982)] 优化 readme 文档和升级POM依赖
  - [[#3991](https://github.com/seata/seata/pull/3991)] 关闭spring boot下无用的fileListener
  - [[#3994](https://github.com/seata/seata/pull/3994)] 优化`tcc_fence_log`表定时删除任务的机制
  - [[#3327](https://github.com/seata/seata/pull/3327)] 支持从etcd3单一key中读取所有配置
  - [[#4001](https://github.com/seata/seata/pull/4001)] 支持从Nacos,Zookeeper,Consul,Etcd3 中读取 yml
  - [[#4017](https://github.com/seata/seata/pull/4017)] 优化文件配置
  - [[#4018](https://github.com/seata/seata/pull/4018)] 优化 Apollo 配置
  - [[#4021](https://github.com/seata/seata/pull/4021)] 优化 Nacos、Consul、Zookeeper、Etcd3 配置
  - [[#4034](https://github.com/seata/seata/pull/4034)] 优化“优化 Nacos、Consul、Zookeeper、Etcd3 配置（#4019）”的单元测试类
  - [[#4055](https://github.com/seata/seata/pull/4055)] 优化NetUtil的getLocalAddress0方法
  - [[#4086](https://github.com/seata/seata/pull/4086)] 分支事务支持懒加载并优化任务调度
  - [[#4056](https://github.com/seata/seata/pull/4056)] 优化 DurationUtil
  - [[#4103](https://github.com/seata/seata/pull/4103)] 减少分支事务注册无需竞争锁时的内存占用 
  - [[#3733](https://github.com/seata/seata/pull/3733)] 优化本地事务下的锁竞争机制 
  - [[#4144](https://github.com/seata/seata/pull/4144)] 支持默认的事务分组配置 
  - [[#4157](https://github.com/seata/seata/pull/4157)] 优化客户端批量发送请求
  - [[#4191](https://github.com/seata/seata/pull/4191)] RPC请求超时时间支持配置化
  - [[#4216](https://github.com/seata/seata/pull/4216)] 非AT模式无须清理undolog表
  - [[#4176](https://github.com/seata/seata/pull/4176)] 优化redis注册中心存储，改用自动过期key替代hash.
  - [[#4196](https://github.com/seata/seata/pull/4196)] TC 批量响应客户端
  - [[#4212](https://github.com/seata/seata/pull/4212)] 控制台接口合并优化
  - [[#4237](https://github.com/seata/seata/pull/4237)] 当所有的before image均为空的时候，跳过checkLock的步骤
  - [[#4251](https://github.com/seata/seata/pull/4251)] 优化部分代码处理
  - [[#4262](https://github.com/seata/seata/pull/4262)] 优化 tcc 模块代码处理
  - [[#4235](https://github.com/seata/seata/pull/4235)] 优化eureka注册中心保存实例信息
  - [[#4277](https://github.com/seata/seata/pull/4277)] 优化Redis-pipeline模式本地事务下的锁竞争机制
  - [[#4284](https://github.com/seata/seata/pull/4284)] 支持MSE-Nacos 的 ak/sk 鉴权方式
  - [[#4299](https://github.com/seata/seata/pull/4299)] 优化异常提示
  - [[#4300](https://github.com/seata/seata/pull/4300)] 优化NettyRemotingServer的close()由DefaultCoordinator来调用，不再额外注册到ServerRunner
  - [[#4270](https://github.com/seata/seata/pull/4270)] 提高全局提交和全局回滚的性能，分支事务清理异步化
  - [[#4307](https://github.com/seata/seata/pull/4307)] 优化在TCC模式减少不必要的全局锁删除
  - [[#4303](https://github.com/seata/seata/pull/4303)] `tcc_fence_log`表悬挂日志记录异步删除
  - [[#4328](https://github.com/seata/seata/pull/4328)] 配置上传脚本支持注释
  - [[#4305](https://github.com/seata/seata/pull/4305)] 优化tc端全局锁获取失败时的日志打印
  - [[#4336](https://github.com/seata/seata/pull/4336)] 添加AT模式不支持的SQL语句异常提示
  - [[#4359](https://github.com/seata/seata/pull/4359)] 支持配置元数据读取环境变量
  - [[#4353](https://github.com/seata/seata/pull/4353)] 为 `seata-all.jar` 瘦身。  
  - [[#4393](https://github.com/seata/seata/pull/4393)] redis & db 模式下启动不需要reload
  - [[#4247](https://github.com/seata/seata/pull/4247)] 在`github/actions`上，添加基于 `java17` 和 `springboot` 各版本的测试
  - [[#4400](https://github.com/seata/seata/pull/4400)] 异步二阶段任务支持并行处理提升效率 
  - [[#4391](https://github.com/seata/seata/pull/4391)] commit/rollback 重试超时事件
  - [[#4409](https://github.com/seata/seata/pull/4409)] 测试类添加版权标题
  - [[#4282](https://github.com/seata/seata/pull/4282)] 优化回滚镜像构建逻辑
  - [[#4407](https://github.com/seata/seata/pull/4407)] file模式下无需延迟删除globasession
  - [[#4436](https://github.com/seata/seata/pull/4436)] 优化file模式下的global session查询接口
  - [[#4431](https://github.com/seata/seata/pull/4431)] 优化redis模式查询globalSession限制查询条数
  - [[#4465](https://github.com/seata/seata/pull/4465)] 优化TC 批量响应客户端模式客户端版本传输方式
  - [[#4469](https://github.com/seata/seata/pull/4469)] 优化控制台db模式下获取配置的方式
  - [[#4478](https://github.com/seata/seata/pull/4478)] 优化 Nacos 配置和注册元数据属性
  - [[#4522](https://github.com/seata/seata/pull/4522)] 优化 GC 参数
  - [[#4517](https://github.com/seata/seata/pull/4517)] 增强失败/超时状态的监控
  - [[#4451](https://github.com/seata/seata/pull/4451)] filesessionmanager改为单例并优化任务线程池处理
  - [[#4551](https://github.com/seata/seata/pull/4551)] 优化 metrics rt 统计问题
  - [[#4574](https://github.com/seata/seata/pull/4574)] 支持 accessKey/secretKey 配置自动注入
  - [[#4583](https://github.com/seata/seata/pull/4583)] DefaultAuthSigner的默认签名加密方法替换为HmacSHA256
  - [[#4591](https://github.com/seata/seata/pull/4591)] 优化开关默认值
  - [[#3780](https://github.com/seata/seata/pull/3780)] 升级 Druid 版本
  - [[#3797](https://github.com/seata/seata/pull/3797)] 支持在`Try` 方法外，由用户自己实例化`BusinessActionContext`，再以`Try`方法入参的形式传入
  - [[#3909](https://github.com/seata/seata/pull/3909)] 优化`collectRowLocks` 方法
  - [[#3763](https://github.com/seata/seata/pull/3763)] 优化 github actions
  - [[#4345](https://github.com/seata/seata/pull/4345)] 修正包目录名
  - [[#4346](https://github.com/seata/seata/pull/4346)] 优化服务器日志并移除lombok
  - [[#4348](https://github.com/seata/seata/pull/4348)] 统一管理maven插件及其版本
  - [[#4354](https://github.com/seata/seata/pull/4354)] 优化saga测试用例
  - [[#4227](https://github.com/seata/seata/pull/4227)] 统一管理依赖的版本，并且升级spring-boot到2.4.13
  - [[#4403](https://github.com/seata/seata/pull/4403)] 禁用saga单测
  - [[#4453](https://github.com/seata/seata/pull/4453)] 升级 eureka-clients 和 xstream 的版本
  - [[#4481](https://github.com/seata/seata/pull/4481)] 优化nacos配置和命名属性
  - [[#4477](https://github.com/seata/seata/pull/4477)] 优化调试级别日志并修复拼写错误
  - [[#4484](https://github.com/seata/seata/pull/4484)] 优化TM/RM注册时TC的日志打印
  - [[#3874](https://github.com/seata/seata/pull/4484)] 增加登记企业，修改图片至alicdn
  - [[#4458](https://github.com/seata/seata/pull/4458)] 修复 metrices 模块 README.md 的配置遗漏问题
  - [[#4482](https://github.com/seata/seata/pull/4482)] 移除重复单词

### test：


 非常感谢以下 contributors 的代码贡献。若有无意遗漏，请报告。

  - [slievrly](https://github.com/slievrly) 
  - [wangliang181230](https://github.com/wangliang181230)
  - [a364176773](https://github.com/a364176773) 
  - [lvekee](https://github.com/lvekee)
  - [caohdgege](https://github.com/caohdgege)
  - [lightClouds917](https://github.com/lightClouds917)
  - [objcoding](https://github.com/objcoding)
  - [siyu](https://github.com/Pinocchio2018)
  - [GoodBoyCoder](https://github.com/GoodBoyCoder)
  - [pengten](https://github.com/pengten)
  - [Bughue](https://github.com/Bughue)
  - [doubleDimple](https://github.com/doubleDimple)
  - [zhaoyuguang](https://github.com/zhaoyuguang)
  - [liuqiufeng](https://github.com/liuqiufeng)
  - [jsbxyyx](https://github.com/jsbxyyx)
  - [lcmvs](https://github.com/lcmvs)
  - [onlinechild](https://github.com/onlinechild)
  - [xjlgod](https://github.com/xjlgod)
  - [h-zhi](https://github.com/h-zhi)
  - [tanzzj](https://github.com/tanzzj)
  - [miaoxueyu](https://github.com/miaoxueyu)
  - [selfishlover](https://github.com/selfishlover)
  - [tuwenlin](https://github.com/tuwenlin)
  - [dmego](https://github.com/dmego)
  - [xiaochangbai](https://github.com/xiaochangbai)
  - [Rubbernecker](https://github.com/Rubbernecker)
  - [ruanun](https://github.com/ruanun)
  - [huan415](https://github.com/huan415)
  - [drgnchan](https://github.com/drgnchan) 
  - [cmonkey](https://github.com/cmonkey)
  - [13414850431](https://github.com/13414850431)
  - [ls9527](https://github.com/ls9527)
  - [xingfudeshi](https://github.com/xingfudeshi)
  - [spilledyear](https://github.com/spilledyear)
  - [kaka2code](https://github.com/kaka2code)
  - [iqinning](https://github.com/iqinning)
  - [yujianfei1986](https://github.com/yujianfei1986)
  - [elrond-g](https://github.com/elrond-g)
  - [jameslcj](https://github.com/jameslcj)
  - [zhouchuhang](https://github.com/zch0214)
  - [xujj](https://github.com/XBNGit)
  - [mengxzh](https://github.com/mengxzh)
  - [portman](https://github.com/iportman)
  - [anselleeyy](https://github.com/anselleeyy)
  - [wangyuewen](https://github.com/2858917634)
  - [imherewait](https://github.com/imherewait)
  - [wfnuser](https://github.com/wfnuser)
  - [zhixing](https://github.com/chenlei3641)

同时，我们收到了社区反馈的很多有价值的issue和建议，非常感谢大家。

   #### Link

   - **Seata:** https://github.com/seata/seata  
   - **Seata-Samples:** https://github.com/seata/seata-samples   
   - **Release:** https://github.com/seata/seata/releases
   - **WebSite:** https://seata.io

</details>
