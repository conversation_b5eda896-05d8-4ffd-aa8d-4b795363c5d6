<!--
    Licensed to the Apache Software Foundation (ASF) under one or more
    contributor license agreements.  See the NOTICE file distributed with
    this work for additional information regarding copyright ownership.
    The ASF licenses this file to You under the Apache License, Version 2.0
    (the "License"); you may not use this file except in compliance with
    the License.  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0
    
    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
-->
所有提交到 2.x 分支的 PR 请在此处登记。

<!-- 请根据PR的类型添加 `变更记录` 到以下对应位置(feature/bugfix/optimize/test) 下 -->

### feature:

- [[#7261](https://github.com/apache/incubator-seata/pull/7261)] 强制进行账户初始化并禁用默认凭据


### bugfix:

- [[#7349](https://github.com/apache/incubator-seata/pull/7349)] 解决 EtcdRegistryServiceImplMockTest 中的空指针异常
- [[#7354](https://github.com/apache/incubator-seata/pull/7354)] 修复lib文件夹中的驱动程序无法加载
- [[#7356](https://github.com/apache/incubator-seata/pull/7356)] 修复 codecov 错误
- [[#7370](https://github.com/apache/incubator-seata/pull/7370)] 修复 ISSUE_TEMPLATE 不可用
- [[#7397](https://github.com/apache/incubator-seata/pull/7397)] 解决空指针和端口绑定错误


### optimize:

- [[#7270](https://github.com/apache/incubator-seata/pull/7270)] 增强 ci 配置
- [[#7282](https://github.com/apache/incubator-seata/pull/7282)] 优化FileRegistryServiceImpl类lookup的NullPointerException问题
- [[#7310](https://github.com/seata/seata/pull/7310)] 优化naming-server中的一些小问题
- [[#7329](https://github.com/apache/incubator-seata/pull/7329)] 将 tomcat 升级到 9.0.100
- [[#7346](https://github.com/apache/incubator-seata/pull/7346)] 去除springweb改为复用事务端口多协议支持http
- [[#7344](https://github.com/apache/incubator-seata/pull/7344)] raft模式提前检查事务大小
- [[#7343](https://github.com/apache/incubator-seata/pull/7343)] 将 tomcat 升级至 9.0.104
- [[#7337](https://github.com/apache/incubator-seata/pull/7337)] 添加 ChannelEventListener 支持以防止内存泄漏
- [[#7344](https://github.com/apache/incubator-seata/pull/7344)] raft模式提前检查事务大小
- [[#7345](https://github.com/apache/incubator-seata/pull/7345)] 为 RegistryFactory 增加空校验与重复类型检查
- [[#7350](https://github.com/apache/incubator-seata/pull/7350)] 优化单测覆盖配置
- [[#7360](https://github.com/apache/incubator-seata/pull/7360)] 更新通道断开连接时的资源清理逻辑
- [[#7363](https://github.com/apache/incubator-seata/pull/7363)] 升级 npmjs 依赖项
- [[#7372](https://github.com/apache/incubator-seata/pull/7372)] 改进忽略许可证标头检查
- [[#7375](https://github.com/apache/incubator-seata/pull/7375)] 优化 discovery 模块的 close 方法
- [[#7388](https://github.com/apache/incubator-seata/pull/7388)] 优化二进制打包目录结构
- [[#7412](https://github.com/apache/incubator-seata/pull/7412)] 适配新版本 Seata 的 Helm 模板
- [[#7414](https://github.com/apache/incubator-seata/pull/7414)] 移除 NettyClientBootstrap 中 defaultEventExecutorGroup
- [[#7415](https://github.com/apache/incubator-seata/pull/7415)] 使用线程池异步处理server http请求
- [[#7418](https://github.com/apache/incubator-seata/pull/7418)] 添加 jackson notice
- [[#7419](https://github.com/apache/incubator-seata/pull/7419)] 添加 Maven 配置文件以支持源码打包
- [[#7428](https://github.com/apache/incubator-seata/pull/7428)] 修改 pmd-check 输出日志为 ERROR 级别
- [[#7430](https://github.com/apache/incubator-seata/pull/7430)] 在netty-http-server中增加了对解析@RequestParam注释的支持
- [[#7445](https://github.com/apache/incubator-seata/pull/7432)] 分离license到server和namingserver
- [[#7426](https://github.com/apache/incubator-seata/pull/7426)] 添加 license header


### security:

- [[#PR_NO](https://github.com/seata/seata/pull/PR_NO)] upgrade XXX


### test:

- [[#7092](https://github.com/apache/incubator-seata/pull/7092)] 修复NacosMockTest测试方法并行导致测试结果被干扰失败的问题
- [[#7098](https://github.com/apache/incubator-seata/pull/7098)] 增加 `seata-common` 模块的测试用例
- [[#7160](https://github.com/apache/incubator-seata/pull/7160)] 在 LowerCaseLinkHashMapTest 中重构测试，以使用参数化单元测试
- [[#7167](https://github.com/apache/incubator-seata/pull/7167)] 重构了 DurationUtilTest 中的测试，以简化并使用参数化单元测试
- [[#7189](https://github.com/apache/incubator-seata/pull/7189)] 修复saga测试用例运行异常
- [[#7197](https://github.com/apache/incubator-seata/pull/7197)] 为 config 模块添加 UT 测试用例
- [[#7199](https://github.com/apache/incubator-seata/pull/7199)] 增加 client processor 单测用例
- [[#7203](https://github.com/apache/incubator-seata/pull/7203)] 重构了 rm.datasource.sql.Druid 和 seata-sqlparser-druid 模块中的测试
- [[#7221](https://github.com/apache/incubator-seata/pull/7221)] 增加 gRPC Encoder/Decoder的测试用例
- [[#7227](https://github.com/apache/incubator-seata/pull/7227)] 为 seata-discovery-consul 增加mock测试
- [[#7233][https://github.com/apache/incubator-seata/pull/7233]] 增加对 seata-discovery-etcd3 的mock测试
- [[#7243](https://github.com/apache/incubator-seata/pull/7243)] 增加对 seata-discovery-eureka的单测
- [[#7255](https://github.com/apache/incubator-seata/pull/7255)] 补充更多seata-discovery-eureka模块的单测提高覆盖率
- [[#7286](https://github.com/apache/incubator-seata/pull/7286)] 重构了 RaftSyncMessageTest 中的 testMsgSerialize 测试，通过拆分为两个独立测试以简化逻辑
- [[#7287](https://github.com/apache/incubator-seata/pull/7287)] 重构了 CodeTest 中的 testGetErrorMsgWithValidCodeReturnsExpectedMsg 测试，以简化并使用参数化单元测试。
- [[#7288](https://github.com/apache/incubator-seata/pull/7288)] 重构了 CodeTest 中的 testSetCodeAndMsgUpdatesValuesCorrectly 测试，以简化并使用参数化单元测试。
- [[#7294](https://github.com/apache/incubator-seata/pull/7294)] 重构了 SqlServerInsertRecognizerTest 中的 testGetInsertParamsValue 测试，通过拆分并使用参数化单元测试进行改进
- [[#7295](https://github.com/apache/incubator-seata/pull/7295)] 重构了 StringUtilsTest 中的 3 个测试，改为使用参数化单元测试
- [[#7205](https://github.com/apache/incubator-seata/issues/7205)] 为 namingserver module 添加单元测试
- [[#7359](https://github.com/apache/incubator-seata/issues/7359)] 合并所有模块的单测报告，准确显示单测覆盖度
- [[#7423](https://github.com/apache/incubator-seata/pull/7423)] 为 org.apache.seata.spring.annotation.scannercheckers 添加单元测试
- [[#7420](https://github.com/apache/incubator-seata/pull/7420)] 为 RemotingFactoryBeanParser 类添加了单元测试
- [[#7379](https://github.com/apache/incubator-seata/issues/7379)] 为 TccAnnotationProcessor 添加了单元测试 
- [[#7422](https://github.com/apache/incubator-seata/pull/7422)] 为 seata-spring-boot-starter 添加了测试
- [[#7433](https://github.com/apache/incubator-seata/pull/7433)] 增加对 GlobalTransactionScanner 添加了测试
- [[#7436](https://github.com/apache/incubator-seata/pull/7436)]  修复namingserver 单测错误
- [[#7435](https://github.com/apache/incubator-seata/pull/7435)] 为测试中的动态服务器端口分配添加通用测试配置
- [[#7432](https://github.com/apache/incubator-seata/pull/7432)] 使用Maven Profile按条件引入Test模块
- [[#7442](https://github.com/apache/incubator-seata/pull/7442)] 增加 saga compatible 模块单测


### refactor:

- [[#7315](https://github.com/apache/incubator-seata/pull/7315)] 重构日志测试，使用ListAppender实现更准确高效的日志捕获


### doc:

- [[#PR_NO](https://github.com/seata/seata/pull/PR_NO)] doc XXX


非常感谢以下 contributors 的代码贡献。若有无意遗漏，请报告。

<!-- 请确保您的 GitHub ID 在以下列表中 -->

- [slievrly](https://github.com/slievrly)
- [Monilnarang](https://github.com/Monilnarang)
- [xingfudeshi](https://github.com/xingfudeshi)
- [wjwang00](https://github.com/wjwang00)
- [YongGoose](https://github.com/YongGoose)
- [JisoLya](https://github.com/JisoLya)
- [YoWuwuuuw](https://github.com/YoWuwuuuw)
- [PleaseGiveMeTheCoke](https://github.com/PleaseGiveMeTheCoke)
- [funky-eyes](https://github.com/funky-eyes)
- [xucq07](https://github.com/xucq07)
- [PengningYang](https://github.com/PengningYang)
- [WangzJi](https://github.com/WangzJi)
- [maple525866](https://github.com/maple525866)
- [YvCeung](https://github.com/YvCeung)
- [jsbxyyx](https://github.com/jsbxyyx)


同时，我们收到了社区反馈的很多有价值的issue和建议，非常感谢大家。
