<!--
    Licensed to the Apache Software Foundation (ASF) under one or more
    contributor license agreements.  See the NOTICE file distributed with
    this work for additional information regarding copyright ownership.
    The ASF licenses this file to You under the Apache License, Version 2.0
    (the "License"); you may not use this file except in compliance with
    the License.  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0
    
    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
-->
### 2.0.0

 [source](https://github.com/seata/seata/archive/v2.0.0.zip) |
 [binary](https://github.com/seata/seata/releases/download/v2.0.0/seata-server-2.0.0.zip) 

<details>
  <summary><mark>Release notes</mark></summary>

### Seata 2.0.0

Seata 2.0.0 发布。

Seata 是一款开源的分布式事务解决方案，提供高性能和简单易用的分布式事务服务。

此版本更新如下：

### feature：
- [[#5165](https://github.com/seata/seata/pull/5165)] TCC结构拆分，支持API方式接入。增加集成层模块（seata-integration-tx-api），对事务流程定义以及代理部分增强。
- [[#5352](https://github.com/seata/seata/pull/5352)] 在TCC Business Action Context中集成jackson和gson序列化功能
- [[#5377](https://github.com/seata/seata/pull/5377)] 使AbstractHttpExecutor类支持PUT方式的请求
- [[#5396](https://github.com/seata/seata/pull/5396)] TC 异常日志指标采集
- [[#5118](https://github.com/seata/seata/pull/5118)] 支持二阶段并行下发执行
- [[#5529](https://github.com/seata/seata/pull/5529)] docker镜像支持注入JVM参数到容器
- [[#3887](https://github.com/seata/seata/pull/3887)] 增加AT模式的SQLServer数据库支持
- [[#4033](https://github.com/seata/seata/pull/4033)] 增加ServerDB存储模式的SQLServer支持
- [[#5717](https://github.com/seata/seata/pull/5717)] 兼容1.4.2及以下版本的file.conf/registry.conf配置
- [[#5842](https://github.com/seata/seata/pull/5842)] 构建docker 镜像时添加相关git信息,方便定位代码关系
- [[#5902](https://github.com/seata/seata/pull/5902)] 支持IPv6网络环境
- [[#5907](https://github.com/seata/seata/pull/5907)] 增加AT模式的PolarDB-X 2.0数据库支持
- [[#5932](https://github.com/seata/seata/pull/5932)] AT模式支持达梦数据库
- [[#5946](https://github.com/seata/seata/pull/5946)] 增加sqlserver对控制台分页接口的适配
- [[#5226](https://github.com/seata/seata/pull/5226)] 支持Raft集群部署和事务存储模式

### bugfix：
- [[#5677](https://github.com/seata/seata/pull/5677)]  修复saga模式下serviceTask入参autoType转化失败问题
- [[#5194](https://github.com/seata/seata/pull/5194)] 修复使用Oracle作为服务端DB存储时的建表失败问题
- [[#5021](https://github.com/seata/seata/pull/5201)] 修复 JDK17 下获取 Spring 原始代理对象失败的问题
- [[#5023](https://github.com/seata/seata/pull/5203)] 修复 `seata-core` 模块传递依赖冲突
- [[#5224](https://github.com/seata/seata/pull/5224)] 修复 oracle初始化脚本索引名重复的问题
- [[#5233](https://github.com/seata/seata/pull/5233)] 修复LoadBalance相关配置不一致的问题
- [[#5245](https://github.com/seata/seata/pull/5245)] 修复不完整的distribution模块依赖
- [[#5239](https://github.com/seata/seata/pull/5239)] 修复当使用JDK代理时，`getConfig` 方法获取部分配置时抛出 `ClassCastException` 异常的问题
- [[#5266](https://github.com/seata/seata/pull/5265)] 修复控制台全局锁查询接口查到了已释放的锁
- [[#5282](https://github.com/seata/seata/pull/5282)] 修复并行rm请求处理时数组索引越界问题
- [[#5294](https://github.com/seata/seata/pull/5294)] 修复AT模式下pgsql/oracle的主键列自增的问题
- [[#5298](https://github.com/seata/seata/pull/5298)] 事务提交或回滚超时不移除global session
- [[#5304](https://github.com/seata/seata/pull/5304)] 移除基于文件存储恢复时的RollbackRetryTimeout事务
- [[#5310](https://github.com/seata/seata/pull/5310)] 修复生成update前后镜像sql不对关键字转义的bug
- [[#5318](https://github.com/seata/seata/pull/5318)] 修复jdk8 中 G1 参数
- [[#5330](https://github.com/seata/seata/pull/5330)] 修复单元测试中发现的bug
- [[#5337](https://github.com/seata/seata/pull/5337)] 修复feature#5165中关于spring使用环境下，多interceptor排序问题，同时修复order一致时无法使用BeforeTransaction(AfterTransaction)事务排序问题
- [[#5347](https://github.com/seata/seata/pull/5347)] 修复控制台打印 `unauthorized error` 问题
- [[#5355](https://github.com/seata/seata/pull/5355)] 修复自定义context-path时的问题
- [[#5362](https://github.com/seata/seata/pull/5362)] 修复当TC端回滚返回RollbackFailed时，自定义FailureHandler的方法未执行
- [[#5372](https://github.com/seata/seata/pull/5372)] 修复客户侧事务提交前超时未执行hook和failureHandler的问题
- [[#4734](https://github.com/seata/seata/pull/4734)] 修复AT模式下新增字段产生的字段找不到
- [[#5426](https://github.com/seata/seata/pull/5426)] 修复不能获取GlobalTransactional注解问题
- [[#5478](https://github.com/seata/seata/pull/5478)] 修复提交事务时事务已完成不抛出异常问题
- [[#5491](https://github.com/seata/seata/pull/5491)] 修复日志中不打印方法名的问题
- [[#5449](https://github.com/seata/seata/pull/5449)] 修复Oracle XA模式 start 重入问题
- [[#5531](https://github.com/seata/seata/pull/5531)] 修复读取logback文件路径错误的问题
- [[#5523](https://github.com/seata/seata/pull/5523)] 修复 GlobalStatus=9 在DB存储模式无法清除的问题
- [[#5558](https://github.com/seata/seata/pull/5558)] 修复mariadb回滚失败的问题
- [[#5556](https://github.com/seata/seata/pull/5556)] 修复 oracle 插入 undolog 失败问题
- [[#5579](https://github.com/seata/seata/pull/5579)] 修复 resourceId 为空时，获取 RM_CHANNELS 空指针问题
- [[#5577](https://github.com/seata/seata/pull/5577)] 修复 grpc拦截器解绑xid失败问题
- [[#5594](https://github.com/seata/seata/pull/5594)] 修复participant情况下的重复日志
- [[#5604](https://github.com/seata/seata/pull/5604)] 修复在DB模式下 `asyncCommit` 和 `queueToRetryCommit` 两个方法总是失败的问题
- [[#5661](https://github.com/seata/seata/pull/5661)] 修复connectionProxyXA连接复用时timeout为null
- [[#5678](https://github.com/seata/seata/pull/5675)] 修复 xxx.grouplist 和 grouplist.xxx 配置项兼容问题
- [[#5715](https://github.com/seata/seata/pull/5715)] 修复取中划线配置项错误问题
- [[#5748](https://github.com/seata/seata/pull/5748)] 修复在某些情况下，业务sql中主键字段名大小写与表元数据中的不一致，导致回滚失败
- [[#5745](https://github.com/seata/seata/pull/5745)] 修复不满足 sofa-rpc 中 setAttachment 方法的参数前缀要求问题
- [[#5772](https://github.com/seata/seata/pull/5762)] 修复TableMetaCache的一些字段类型，避免溢出
- [[#5787](https://github.com/seata/seata/pull/5794)] 解决redis作为注册中心时cluster无法自定义的BUG
- [[#5810](https://github.com/seata/seata/pull/5810)] 修复druid依赖冲突导致的XA事务开始异常与回滚失败
- [[#5821](https://github.com/seata/seata/pull/5821)] 修复insert executor对关键字未转义的问题
- [[#5835](https://github.com/seata/seata/pull/5835)] bugfix: 修复当 XA 事务失败回滚后，TC 还会继续重试回滚的问题
- [[#5881](https://github.com/seata/seata/pull/5880)] 修复事务回滚时锁未删除的问题
- [[#5930](https://github.com/seata/seata/pull/5930)] 修复存储为redis哨兵模式下哨兵密码缺失的问题
- [[#5958](https://github.com/seata/seata/pull/5958)] 在二阶段提交状态下发生重选时需要进行解除全局锁
- [[#5971](https://github.com/seata/seata/pull/5971)] 修复某些未弃用的配置显示"已弃用"
- [[#5977](https://github.com/seata/seata/pull/5977)] 修复当raft server关闭时,rpc server未关闭的问题
- [[#5954](https://github.com/seata/seata/pull/5954)] 修复保存的分支会话状态与实际的分支会话状态不一致的问题
- [[#5990](https://github.com/seata/seata/pull/5990)] 修复redis sentinel master node 宕机时，lua脚本未同步的问题
- [[#5887](https://github.com/seata/seata/pull/5887)] 修复全局事务钩子重复执行
- [[#6018](https://github.com/seata/seata/pull/6018)] 修复错误的 metric 上报
- [[#6024](https://github.com/seata/seata/pull/6024)] 修复控制台点击事务信息页面中的"查看全局锁"按钮之后白屏的问题
- [[#6015](https://github.com/seata/seata/pull/6015)] 修复在spring环境下无法集成dubbo
- [[#6049](https://github.com/seata/seata/pull/6049)] 修复客户端在raft注册中心类型下，网络中断时，watch线程未暂停一秒等待重试的问题
- [[#6050](https://github.com/seata/seata/pull/6050)] 修改 RaftServer#destroy 为等待所有关闭流程结束


### optimize：
- [[#6033](https://github.com/seata/seata/pull/6033)] 优化HSFRemotingParser中isReference判断逻辑，去掉关于FactoryBean的无用判断
- [[#5966](https://github.com/seata/seata/pull/5966)] Saga 表达式解耦并统一格式
- [[#5928](https://github.com/seata/seata/pull/5928)] 增加Saga模式状态机语义验证阶段
- [[#5208](https://github.com/seata/seata/pull/5208)] 优化多次重复获取Throwable#getCause问题
- [[#5212](https://github.com/seata/seata/pull/5212)] 优化不合理的日志信息级别
- [[#5237](https://github.com/seata/seata/pull/5237)] 优化异常日志打印(EnhancedServiceLoader.loadFile#cahtch)
- [[#5243](https://github.com/seata/seata/pull/5243)] 升级 kryo 5.4.0 优化对jdk17的兼容性
- [[#5153](https://github.com/seata/seata/pull/5153)] 只允许AT去尝试跨RM获取channel
- [[#5177](https://github.com/seata/seata/pull/5177)] 如果 `server.session.enable-branch-async-remove` 为真，异步删除分支，同步解锁。
- [[#4858](https://github.com/seata/seata/pull/4858)] 重构优化 SessionManager 用法
- [[#4881](https://github.com/seata/seata/pull/4881)] 重新划分 SessionManager和SessionLifecycleListener 用法
- [[#5273](https://github.com/seata/seata/pull/5273)] 优化`protobuf-maven-plugin`插件的编译配置，解决高版本的命令行过长问题
- [[#5278](https://github.com/seata/seata/pull/5278)] 清理sessionmanager多例模式遗留代码
- [[#5302](https://github.com/seata/seata/pull/5302)] 移除启动脚本的-Xmn参数
- [[#4880](https://github.com/seata/seata/pull/4880)] 优化提交和回滚遇到异常时的日志输出
- [[#5322](https://github.com/seata/seata/pull/5322)] 优化SPI加载日志
- [[#5323](https://github.com/seata/seata/pull/5323)] 为全局事务超时日志添加时间信息
- [[#5328](https://github.com/seata/seata/pull/5333)] 为全局事务和事务存储的Redis模式，增加对应的lua实现
- [[#5341](https://github.com/seata/seata/pull/5341)] 优化 gRPC TCC模式
- [[#5342](https://github.com/seata/seata/pull/5342)] 优化 TCC fence log 清理定时任务的 delay 参数值检查
- [[#5325](https://github.com/seata/seata/pull/5325)] 添加配置中心、注册中心类型以及存储模式日志信息
- [[#5351](https://github.com/seata/seata/pull/5351)] 优化 TCC 模式下的 RPC filter
- [[#5354](https://github.com/seata/seata/pull/5354)] 重构 RPC 集成模块
- [[#5370](https://github.com/seata/seata/pull/5370)] 优化事务失败处理 handler
- [[#5461](https://github.com/seata/seata/pull/5461)] 优化 license workflow
- [[#5464](https://github.com/seata/seata/pull/5464)] 修复saga模式全局事务状态始终为Begin的问题
- [[#5456](https://github.com/seata/seata/pull/5456)] 重构 ColumnUtils 和 EscapeHandler
- [[#5438](https://github.com/seata/seata/pull/5438)] 优化code style检测属性
- [[#5471](https://github.com/seata/seata/pull/5471)] 优化客户侧事务日志
- [[#5485](https://github.com/seata/seata/pull/5485)] 优化Server日志输出
- [[#4907](https://github.com/seata/seata/pull/4907)] 调整二阶段result线程池大小及优化代码
- [[#5487](https://github.com/seata/seata/pull/5487)] 将branchsession中的lockholder增加final修饰
- [[#5519](https://github.com/seata/seata/pull/5519)] 优化 Oracle FenceHandler
- [[#5501](https://github.com/seata/seata/pull/5501)] 支持乐观锁方式更新事务状态
- [[#5419](https://github.com/seata/seata/pull/5419)] 优化镜像发布流水线支持jdk8/17和支持maven 3.9.0
- [[#5549](https://github.com/seata/seata/pull/5549)] 优化 gpg key 和 发布流水线
- [[#5576](https://github.com/seata/seata/pull/5576)] 仅当 useTCCFence 设置为 true 时，才开启 Fence 表清理任务
- [[#5623](https://github.com/seata/seata/pull/5623)] 优化异步提交线程和重试线程之间可能存在的冲突
- [[#5553](https://github.com/seata/seata/pull/5553)] 支持表和列元数据大小写敏感设置
- [[#5644](https://github.com/seata/seata/pull/5644)] 优化Server日志输出
- [[#5680](https://github.com/seata/seata/pull/5680)] 优化大小写转义符
- [[#5714](https://github.com/seata/seata/pull/5714)] 优化分布式锁竞争日志
- [[#5723](https://github.com/seata/seata/pull/5723)] 优化docker镜像的默认时区
- [[#5779](https://github.com/seata/seata/pull/5779)] 删除无用的输出日志并统一日志输出路径
- [[#5802](https://github.com/seata/seata/pull/5802)] 优化server端事务隔离级别为读已提交
- [[#5783](https://github.com/seata/seata/pull/5783)] 支持nacos上application name配置
- [[#5524](https://github.com/seata/seata/pull/5524)] 支持 seata-server.sh 中的更多操作命令
- [[#5836](https://github.com/seata/seata/pull/5836)] 分离mariadb和mysql的AT实现
- [[#5869](https://github.com/seata/seata/pull/5869)] 优化一些小的语法
- [[#5885](https://github.com/seata/seata/pull/5885)] 优化ConnectionProxyXA中的日志
- [[#5894](https://github.com/seata/seata/pull/5894)] 移除无license组件
- [[#5895](https://github.com/seata/seata/pull/5895)] 移除7z压缩支持
- [[#5896](https://github.com/seata/seata/pull/5896)] 移除 mariadb.jdbc 依赖
- [[#5384](https://github.com/seata/seata/pull/5384)] 统一版本号管理，只需维护 `build/pom.xml` 中的版本号即可。
- [[#5419](https://github.com/seata/seata/pull/5419)] 发布基于多个java版本的docker镜像
- [[#5829](https://github.com/seata/seata/pull/5829)] 修正 `codecov chart` 不展示的问题
- [[#5878](https://github.com/seata/seata/pull/5878)] 优化 `httpcore` 和 `httpclient` 的依赖定义
- [[#5917](https://github.com/seata/seata/pull/5917)] 升级 native-lib-loader 版本
- [[#5926](https://github.com/seata/seata/pull/5926)] 优化一些与 Apollo 相关的脚本
- [[#5938](https://github.com/seata/seata/pull/5938)] 支持 jmx 监控配置
- [[#5951](https://github.com/seata/seata/pull/5951)] 删除在 jdk17 中不支持的配置项
- [[#5959](https://github.com/seata/seata/pull/5959)] 修正代码风格问题及去除无用的类引用
- [[#6002](https://github.com/seata/seata/pull/6002)] 移除fst序列化模块
- [[#6045](https://github.com/seata/seata/pull/6045)] 优化MySQL衍生数据库判断逻辑
- [[#6342](https://github.com/seata/seata/pull/6342)] 兼容integration-tx-api模块



### security:
- [[#5642](https://github.com/seata/seata/pull/5642)] 增加Hessian 序列化黑白名单
- [[#5694](https://github.com/seata/seata/pull/5694)] 修复若干Node.js依赖安全漏洞
- [[#5801](https://github.com/seata/seata/pull/5801)] 修复Java依赖安全漏洞
- [[#5805](https://github.com/seata/seata/pull/5805)] 修复序列化漏洞
- [[#5868](https://github.com/seata/seata/pull/5868)] 修复npm package漏洞
- [[#5916](https://github.com/seata/seata/pull/5916)] 修复npm package漏洞
- [[#5942](https://github.com/seata/seata/pull/5942)] 升级依赖版本
- [[#5987](https://github.com/seata/seata/pull/5987)] 升级依赖版本
- [[#6013](https://github.com/seata/seata/pull/6013)] 升级seata-server依赖的spring版本

### test：
- [[#5308](https://github.com/seata/seata/pull/5308)] 添加单元测试用例 [FileLoader, ObjectHolder, StringUtils]
- [[#5309](https://github.com/seata/seata/pull/5309)] 添加单元测试用例 [ArrayUtils, ConfigTools, MapUtil]
- [[#5335](https://github.com/seata/seata/pull/5335)] 添加单元测试用例 [EnhancedServiceLoader,ExtensionDefinition,SizeUtilTest,ReflectionUtil,LowerCaseLinkHashMap,FileLoader,ObjectHolder]
- [[#5366](https://github.com/seata/seata/pull/5366)] 修复 UpdateExecutorTest 单测失败问题
- [[#5383](https://github.com/seata/seata/pull/5383)] 修复多Spring版本测试失败
- [[#5391](https://github.com/seata/seata/pull/5391)] 添加 config 模块的单元测试用例
- [[#5428](https://github.com/seata/seata/pull/5428)] 修复 FileTransactionStoreManagerTest 单测失败问题
- [[#5622](https://github.com/seata/seata/pull/5622)] 添加单元测试用例 [ExporterType, RegistryType]
- [[#5637](https://github.com/seata/seata/pull/5637)] 添加单元测试用例 [BatchResultMessage, HeartbeatMessage, RegisterRMResponse, ResultCode, RegisterTMResponse, MergeResultMessage, MergedWarpMessage, Version]
- [[#5893](https://github.com/seata/seata/pull/5893)] 移除 sofa 测试用例
- [[#5845](https://github.com/seata/seata/pull/5845)] 升级 `druid` 版本，并添加 `test-druid.yml` 用于测试seata与druid各版本的兼容性。
- [[#5863](https://github.com/seata/seata/pull/5863)] 修复单元测试在Java21下无法正常运行的问题。
- [[#5986](https://github.com/seata/seata/pull/5986)] 修复 zookeeper 单测失败问题
- [[#5995](https://github.com/seata/seata/pull/5995)] 添加 RaftClusterMetadataMsg 模块的单元测试用例
- [[#6001](https://github.com/seata/seata/pull/6001)] 添加 RaftMsgExecute 模块 branch 包下的单元测试用例
- [[#5996](https://github.com/seata/seata/pull/5996)] 添加 RaftMsgExecute 模块 global 包下的单元测试用例
- [[#6003](https://github.com/seata/seata/pull/6003)] 添加 RaftMsgExecute 模块 lock 包下的单元测试用例
- [[#6009](https://github.com/seata/seata/pull/6009)] 添加RaftServerFactory的单元测试用例


### Contributors:

非常感谢以下 contributors 的代码贡献。若有无意遗漏，请报告。

- [slievrly](https://github.com/slievrly)
- [xssdpgy](https://github.com/xssdpgy)
- [albumenj](https://github.com/albumenj)
- [PeppaO](https://github.com/PeppaO)
- [yuruixin](https://github.com/yuruixin)
- [CrazyLionLi](https://github.com/JavaLionLi)
- [xingfudeshi](https://github.com/xingfudeshi)
- [Bughue](https://github.com/Bughue)
- [pengten](https://github.com/pengten)
- [wangliang181230](https://github.com/wangliang181230)
- [GoodBoyCoder](https://github.com/GoodBoyCoder)
- [funky-eyes](https://github.com/funky-eyes)
- [isharpever](https://github.com/isharpever)
- [mxsm](https://github.com/mxsm)
- [liuqiufeng](https://github.com/liuqiufeng)
- [l81893521](https://github.com/l81893521)
- [dmego](https://github.com/dmego)
- [zsp419](https://github.com/zsp419)
- [tuwenlin](https://github.com/tuwenlin)
- [sixlei](https://github.com/sixlei)
- [yixia](https://github.com/wt-better)
- [capthua](https://github.com/capthua)
- [robynron](https://github.com/robynron)
- [XQDD](https://github.com/XQDD)
- [Weelerer](https://github.com/Weelerer)
- [Ifdevil](https://github.com/Ifdevil)
- [iquanzhan](https://github.com/iquanzhan)
- [leizhiyuan](https://github.com/leizhiyuan)
- [Aruato](https://github.com/Aruato)
- [ggbocoder](https://github.com/ggbocoder)
- [ptyin](https://github.com/ptyin)
- [jsbxyyx](https://github.com/jsbxyyx)
- [xxxcrel](https://github.com/xxxcrel)



同时，我们收到了社区反馈的很多有价值的issue和建议，非常感谢大家。


#### Link

- **Seata:** https://github.com/seata/seata  
- **Seata-Samples:** https://github.com/seata/seata-samples   
- **Release:** https://github.com/seata/seata/releases
- **WebSite:** https://seata.io

</details>
