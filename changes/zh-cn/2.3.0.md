<!--
    Licensed to the Apache Software Foundation (ASF) under one or more
    contributor license agreements.  See the NOTICE file distributed with
    this work for additional information regarding copyright ownership.
    The ASF licenses this file to You under the Apache License, Version 2.0
    (the "License"); you may not use this file except in compliance with
    the License.  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0
    
    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
-->
### 2.3.0

<details>
  <summary><mark>Release notes</mark></summary>

### Apache Seata(incubating) 2.3.0

Apache Seata(incubating) 2.3.0 发布。

Apache Seata(incubating) 是一款开源的分布式事务解决方案，提供高性能和简单易用的分布式事务服务。
此版本更新如下：

### feature:

- [[#6904](https://github.com/apache/incubator-seata/pull/6904)] 增加Fastjson2序列化Rpc消息支持
- [[#6876](https://github.com/apache/incubator-seata/pull/6876)] 支持人大金仓数据库(kingbase)
- [[#6881](https://github.com/apache/incubator-seata/pull/6881)] client和server支持grpc协议
- [[#6864](https://github.com/apache/incubator-seata/pull/6864)] 支持神通数据库(oscar)
- [[#6974](https://github.com/apache/incubator-seata/pull/6974)] 支持UndoLog的fastjson2序列化方式
- [[#6992](https://github.com/apache/incubator-seata/pull/6992)] 支持grpc序列化器
- [[#6995](https://github.com/apache/incubator-seata/pull/6995)] 升级过时的 npmjs 依赖
- [[#6973](https://github.com/apache/incubator-seata/pull/6973)] 支持saga注解化
- [[#6926](https://github.com/apache/incubator-seata/pull/6926)] 支持Raft节点间的SSL通信

### bugfix:

- [[#6899](https://github.com/apache/incubator-seata/pull/6899)] 修复file.conf打包后的读取
- [[#6890](https://github.com/apache/incubator-seata/pull/6890)] 修复saga设计json转标准json过程中: 子状态机补偿节点无法被识别
- [[#6907](https://github.com/apache/incubator-seata/pull/6907)] 修复Codecov未生成报告的问题
- [[#6923](https://github.com/apache/incubator-seata/pull/6923)] 增强 401 错误处理，通过刷新令牌
- [[#6925](https://github.com/apache/incubator-seata/pull/6925)] 修复Raft模式下，Follower崩溃可能导致Client继续使用过期令牌的问题
- [[#6932](https://github.com/apache/incubator-seata/pull/6932)] 修复开启本地事务时file&raft模式下锁争抢失败未退出导致可能出现残留锁
- [[#6940](https://github.com/apache/incubator-seata/pull/6940)] 修复NacosRegistry lookup 行为 transactionServiceGroup
  为空导致 NPE 错误
- [[#6943](https://github.com/apache/incubator-seata/pull/6943)] 修复并发状态下 `convertBranchSession` 转换报错问题
- [[#6948](https://github.com/apache/incubator-seata/pull/6948)] 修复在ARM64平台下CI构建出错的问题
- [[#6947](https://github.com/apache/incubator-seata/pull/6947)] 修复nacos注册中心查询可用地址时的空指针问题
- [[#6984](https://github.com/apache/incubator-seata/pull/6984)] 修复 openjdk23 版本下无法构建 docker 镜像的问题
- [[#6994](https://github.com/apache/incubator-seata/pull/6994)] 修复updateJoin语句未更新到数据时prepareUndoLog异常
- [[#7005](https://github.com/apache/incubator-seata/pull/7005)] 修复Raft模式下两阶段并发可能导致NPE的问题
- [[#7010](https://github.com/apache/incubator-seata/pull/7010)] 修复使用达梦数据库时删除undolog发生SQL语法错误
- [[#7022](https://github.com/apache/incubator-seata/pull/7022)] 修复 `application.raft.example.yml`的 `store.mode`属性
- [[#7025](https://github.com/apache/incubator-seata/pull/7025)] 修复vGroupMappingManager未初始化的问题
- [[#7044](https://github.com/apache/incubator-seata/pull/7044)] 修复TableMeta在数据源关闭后刷新错误问题
- [[#7117](https://github.com/apache/incubator-seata/pull/7117)] 修复 seata.server.raft.ssl 前缀不存在的问题
- [[#7127](https://github.com/apache/incubator-seata/pull/7127)] 修复saga注解化导致的server branchType解码失败问题


### optimize:

- [[#6826](https://github.com/apache/incubator-seata/pull/6826)] 移除只读XA事务的分支注册操作
- [[#6874](https://github.com/apache/incubator-seata/pull/6874)] modify the version to 2.3.0-SNAPSHOT
- [[#6892](https://github.com/apache/incubator-seata/pull/6892)] 升级 console 模块 npmjs 版本
- [[#6874](https://github.com/apache/incubator-seata/pull/6874)] 修改版本为2.3.0-SNAPSHOT
- [[#6883](https://github.com/apache/incubator-seata/pull/6874)] 删除代码中无用对象的创建
- [[#6892](https://github.com/apache/incubator-seata/pull/6892)] 升级 npmjs 版本
- [[#6889](https://github.com/apache/incubator-seata/pull/6889)] 修正单词拼写错误
- [[#6898](https://github.com/apache/incubator-seata/pull/6898)] 升级 saga 模块 npmjs 版本
- [[#6879](https://github.com/apache/incubator-seata/pull/6879)] 修复日志参数不匹配问题
- [[#6898](https://github.com/apache/incubator-seata/pull/6898)] 升级 saga 模块 npmjs 版本
- [[#6902](https://github.com/apache/incubator-seata/pull/6900)] 优化 readme 文档
- [[#6807](https://github.com/apache/incubator-seata/pull/6807)] 分离merge消息使其能完全并行处理
- [[#6905](https://github.com/apache/incubator-seata/pull/6905)] 移除构建期不兼容的 license
- [[#6906](https://github.com/apache/incubator-seata/pull/6906)] h2依赖添加test scope
- [[#6911](https://github.com/apache/incubator-seata/pull/6911)] 修正项目中的部分拼写错误
- [[#6918](https://github.com/apache/incubator-seata/pull/6918)] 使用eclipse-temurin的openjdk镜像作为基础镜像
- [[#6938](https://github.com/apache/incubator-seata/pull/6938)] 更新 README.md 中的社区联系信息
- [[#6950](https://github.com/apache/incubator-seata/pull/6950)] 移除JVM参数app.id
- [[#6959](https://github.com/apache/incubator-seata/pull/6959)] 修正 `seata-http-jakarta`的模块命名和描述
- [[#6991](https://github.com/apache/incubator-seata/pull/6991)] gRPC协议序列化默认值为protobuf
- [[#6996](https://github.com/apache/incubator-seata/pull/6996)] 优化 AT 事务模式锁释放逻辑
- [[#6993](https://github.com/apache/incubator-seata/pull/6993)] 优化 metrics 指标
- [[#6995](https://github.com/apache/incubator-seata/pull/6995)] 升级过时的 npmjs 依赖
- [[#6996](https://github.com/apache/incubator-seata/pull/6996)] 优化 AT 事务模式锁释放逻辑
- [[#7023](https://github.com/apache/incubator-seata/pull/7023)] 优化快速失败
- [[#7027](https://github.com/apache/incubator-seata/pull/7027)] raft模式下reload行为与file保持一致
- [[#6891](https://github.com/apache/incubator-seata/pull/6891)] 增加 StateType 类型
- [[#7040](https://github.com/apache/incubator-seata/pull/7040)] 优化ConfigurationFactory加载的打印信息
- [[#7046](https://github.com/apache/incubator-seata/pull/7046)] 去除spring-webmvc的依赖冲突
- [[#7043](https://github.com/apache/incubator-seata/pull/7043)] 在获取不到mq的sendResult时，直接完成回滚
- [[#7051](https://github.com/apache/incubator-seata/pull/7051)] 为namingserver模块添加Jib支持以构建Docker镜像
- [[#7054](https://github.com/apache/incubator-seata/pull/7054)] file模式中竞争不到锁时输出持有者的xid
- [[#7154](https://github.com/apache/incubator-seata/pull/7154)] 移除未使用的依赖
- [[#7153](https://github.com/apache/incubator-seata/pull/7153)] 升级 tomcat-embed 版本至 9.0.98
- [[#7152](https://github.com/apache/incubator-seata/pull/7152)] 移除 org.eclipse.jetty 依赖
- [[#7151](https://github.com/apache/incubator-seata/pull/7151)] 升级 xstream 版本至 1.4.21

### refactor:

- [[#7017](https://github.com/apache/incubator-seata/pull/7017)] 移除 seata-server 模块的依赖
- [[#7155](https://github.com/apache/incubator-seata/pull/7155)] 重构不满足 license 要求的代码

### test:

- [[#6869](https://github.com/apache/incubator-seata/pull/6869)] 增加`seata-core`测试用例覆盖率
- [[#6927](https://github.com/apache/incubator-seata/pull/6927)] 增加`seata-rocketmq`模块的测试用例
- [[#7018](https://github.com/apache/incubator-seata/pull/7018)] 增加 `seata-tm` 模块的测试用例
- [[#7030](https://github.com/apache/incubator-seata/pull/7030)] 增加 `seata-common` 模块的测试用例

非常感谢以下 contributors 的代码贡献。若有无意遗漏，请报告。

<!-- 请确保您的 GitHub ID 在以下列表中 -->

- [slievrly](https://github.com/slievrly)
- [GoodBoyCoder](https://github.com/GoodBoyCoder)
- [funky-eyes](https://github.com/funky-eyes)
- [dk2k](https://github.com/dk2k)
- [MaoMaoandSnail](https://github.com/MaoMaoandSnail)
- [yougecn](https://github.com/yougecn)
- [arrrnold17](https://github.com/arrrnold17)
- [xjlgod](https://github.com/xjlgod)
- [PleaseGiveMeTheCoke](https://github.com/PleaseGiveMeTheCoke)
- [dsomehan](https://github.com/dsomehan)
- [psxjoy](https://github.com/psxjoy)
- [xingfudeshi](https://github.com/xingfudeshi)
- [o-jimin](https://github.com/o-jimin)
- [lixingjia77](https://github.com/lixingjia77)
- [whaon](https://github.com/whaon)
- [YvCeung](https://github.com/YvCeung)
- [jsbxyyx](https://github.com/jsbxyyx)
- [lightClouds917](https://github.com/lightClouds917)
- [Muluo-cyan](https://github.com/Muluo-cyan) 
- [yixia](https://github.com/wt-better)
- [ChinaJeckXu](https://github.com/ChinaJeckXu)
- [YongGoose](https://github.com/YongGoose)

同时，我们收到了社区反馈的很多有价值的issue和建议，非常感谢大家。
