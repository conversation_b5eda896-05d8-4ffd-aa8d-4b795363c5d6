<!--
    Licensed to the Apache Software Foundation (ASF) under one or more
    contributor license agreements.  See the NOTICE file distributed with
    this work for additional information regarding copyright ownership.
    The ASF licenses this file to You under the Apache License, Version 2.0
    (the "License"); you may not use this file except in compliance with
    the License.  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0
    
    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
-->
所有提交到 2.x 分支的 PR 请在此处登记。

<!-- 请根据PR的类型添加 `变更记录` 到以下对应位置(feature/bugfix/optimize/test) 下 -->

### feature:
- [[#6370](https://github.com/seata/seata/pull/6370)] seata saga spring接耦、架构优化。
- [[#6205](https://github.com/apache/incubator-seata/pull/6205)] 提供mock server
- [[#6169](https://github.com/apache/incubator-seata/pull/6169)] 支持新版本状态机设计器
- [[#6230](https://github.com/apache/incubator-seata/pull/6230)] 支持RocketMQ消息事务
- [[#6326](https://github.com/apache/incubator-seata/pull/6326)] 支持raft节点间的元数据同步
- [[#6415](https://github.com/apache/incubator-seata/pull/6415)] 支持 Saga 设计器的自动布局

### bugfix:
- [[#6090](https://github.com/apache/incubator-seata/pull/6090)] 修复tcc切面异常处理过程，不对内部调用异常做包装处理，直接向外抛出
- [[#6075](https://github.com/apache/incubator-seata/pull/6075)] 修复镜像SQL对于on update列没有添加表别名的问题
- [[#6086](https://github.com/apache/incubator-seata/pull/6086)] 修复oracle alias 解析异常
- [[#6085](https://github.com/apache/incubator-seata/pull/6085)] 修复jdk9+版本编译后，引入后ByteBuffer#flip NoSuchMethodError的问题
- [[#6101](https://github.com/apache/incubator-seata/pull/6101)] 修复在dubbo 3.x的版本中, 消费者端不能生成tcc代理的问题
- [[#6077](https://github.com/apache/incubator-seata/pull/6077)] 修复表存在复合主键索引导致无法回滚问题
- [[#6121](https://github.com/apache/incubator-seata/pull/6121)] 修复回滚分支事务时没有按照时间排序的问题
- [[#6182](https://github.com/apache/incubator-seata/pull/6182)] 修复在ci中guava-32.0.0-jre.jar zip文件为空的问题
- [[#6196](https://github.com/apache/incubator-seata/pull/6196)] 修复asf配置格式错误的问题
- [[#6143](https://github.com/apache/incubator-seata/pull/6143)] 修复优雅停机
- [[#6204](https://github.com/apache/incubator-seata/pull/6204)] 修复错误配置问题
- [[#6248](https://github.com/apache/incubator-seata/pull/6248)] 修复JDBC resultSet, statement, connection关闭顺序
- [[#6261](https://github.com/apache/incubator-seata/pull/6261)] at模式支持pgsql集群模式url
- [[#6256](https://github.com/apache/incubator-seata/pull/6256)] 修复在seata-all sdk下，raft-discovery模块不能读取registry.conf的配置的问题
- [[#6232](https://github.com/apache/incubator-seata/pull/6232)] 修复在mysql的json类型下出现Cannot create a JSON value from a string with CHARACTER SET 'binary'问题
- [[#6278](https://github.com/apache/incubator-seata/pull/6278)] 修复 ProtocolV1SerializerTest 失败问题
- [[#6324](https://github.com/apache/incubator-seata/pull/6324)] 修复 Parse protocol file failed
- [[#6331](https://github.com/apache/incubator-seata/pull/6331)] 修复TCC嵌套事务不能同时添加TwoPhaseBusinessAction和GlobalTransactional两个注解的问题
- [[#6354](https://github.com/apache/incubator-seata/pull/6354)] 修复动态升降级不能正常工作问题
- [[#6363](https://github.com/apache/incubator-seata/pull/6363)] 修复docker镜像中的已知问题
- [[#6372](https://github.com/apache/incubator-seata/pull/6372)] 修复初始化sql文件postgresql.sql 索引名称冲突问题
- [[#6380](https://github.com/apache/incubator-seata/pull/6380)] 修复针对sql server检查UNDO_LOG表是否存在时的SQL异常
- [[#6385](https://github.com/apache/incubator-seata/pull/6385)] 修复Role.Participant不执行hook但会清理的问题
- [[#6465](https://github.com/apache/incubator-seata/pull/6465)] 修复2.0下saga模式的context replay丢失start问题
- [[#6469](https://github.com/apache/incubator-seata/pull/6469)] 修复在sqlserver数据库下[lock_table]数据表的插入操作sql中存在的错误
- [[#6496](https://github.com/apache/incubator-seata/pull/6496)] 修复XA执行长时间SQL(或死锁SQL)没有完成回滚就释放连接
- [[#6493](https://github.com/apache/incubator-seata/pull/6493)] 修复当使用数据库为SQLServer时seata server的SQL报错
- [[#6497](https://github.com/apache/incubator-seata/pull/6497)] 修复自动装配时的seata tcc 配置类
- [[#6554](https://github.com/apache/incubator-seata/pull/6554)] 修复序列化器不固定使用对应配置序列化器的问题
- [[#6555](https://github.com/apache/incubator-seata/pull/6555)] 修复businessActionContext对io seata包的不兼容
- [[#6553](https://github.com/apache/incubator-seata/pull/6553)] 修复执行完 'ServiceTask' 后无法应用任何评估器的问题
- [[#6575](https://github.com/apache/incubator-seata/pull/6575)] 修复io.seata ActionInterceptorHandler误使用了org.apache.seata BusinessActionContextParameter类的问题


### optimize:
- [[#6031](https://github.com/apache/incubator-seata/pull/6031)] 添加undo_log表的存在性校验
- [[#6089](https://github.com/apache/incubator-seata/pull/6089)] 修改RaftServerFactory语义并删除不必要的单例构建
- [[#4473](https://github.com/apache/incubator-seata/pull/4473)] rm appdata大小限制
- [[#6071](https://github.com/apache/incubator-seata/pull/6071)] 添加git信息到JAR包中
- [[#6042](https://github.com/apache/incubator-seata/pull/6042)] 增加raft模式鉴权机制
- [[#6091](https://github.com/apache/incubator-seata/pull/6091)] 优化raft鉴权时获取tc地址的方式
- [[#6098](https://github.com/apache/incubator-seata/pull/6098)] 优化acquireMetadata方法的重试逻辑
- [[#6034](https://github.com/apache/incubator-seata/pull/6034)] 使用helm图表进行部署时使用命令行中的命名空间
- [[#6116](https://github.com/apache/incubator-seata/pull/6034)] 移除 lgtm.com 
- [[#6164](https://github.com/apache/incubator-seata/pull/6164)] redis 注册中心推空保护优化
- [[#6174](https://github.com/apache/incubator-seata/pull/6174)] 增加 ASF 基础配置
- [[#6148](https://github.com/apache/incubator-seata/pull/6148)] 支持 Nacos ram role 鉴权方式
- [[#6181](https://github.com/apache/incubator-seata/pull/6181)] 更新贡献指引文档
- [[#6179](https://github.com/apache/incubator-seata/pull/6179)] 移除 <AUTHOR>
- [[#6176](https://github.com/apache/incubator-seata/pull/6176)] 更新源文件header信息
- [[#6178](https://github.com/apache/incubator-seata/pull/6178)] 更新Apache License头信息
- [[#6186](https://github.com/apache/incubator-seata/pull/6186)] 更新README.md（更新邮件列表和一些生态访问链接）
- [[#6184](https://github.com/apache/incubator-seata/pull/6184)] 更新NOTICE文件
- [[#6192](https://github.com/apache/incubator-seata/pull/6192)] 移除无用文件
- [[#6194](https://github.com/apache/incubator-seata/pull/6194)] 修复 asf.yaml 解析错误问题
- [[#5399](https://github.com/apache/incubator-seata/pull/5399)] 分支注册只在RM端
- [[#6154](https://github.com/apache/incubator-seata/pull/6154)] 控制台日志优化 "kubectl logs -f"
- [[#6116](https://github.com/apache/incubator-seata/pull/6116)] 重写NettyPoolKey的hashcode和equals，修复了channel对象池重复构建问题
- [[#6195](https://github.com/apache/incubator-seata/pull/6195)] 更新 change log 中的 seata url 为 apache/incubator-seata
- [[#6200](https://github.com/apache/incubator-seata/pull/6200)] 去除required_status_checks检查
- [[#6201](https://github.com/apache/incubator-seata/pull/6201)] 恢复required_status_checks但去除context校验
- [[#6218](https://github.com/apache/incubator-seata/pull/6218)] 移除Seata-Docker链接
- [[#6227](https://github.com/apache/incubator-seata/pull/6227)] 校验pk中不含逗号
- [[#6004](https://github.com/apache/incubator-seata/pull/6004)] 优化RM,TM连接server快速失败
- [[#6243](https://github.com/apache/incubator-seata/pull/6243)] 优化控制台页眉中的链接
- [[#6238](https://github.com/apache/incubator-seata/pull/6238)] 文件合规优化
- [[#6239](https://github.com/apache/incubator-seata/pull/6239)] 更新security policy，disclaimer 和 notice 文件
- [[#6245](https://github.com/apache/incubator-seata/pull/6245)] file模式下，在配置中心的spring配置改变时，使应用程序中的配置生效
- [[#6247](https://github.com/apache/incubator-seata/pull/6247)] 优化 asf.yml 配置
- [[#6259](https://github.com/apache/incubator-seata/pull/6259)] 修改全局会话大小超过配置的错误消息
- [[#6264](https://github.com/apache/incubator-seata/pull/6264)] 修复 jib-maven-plugin 编译失败问题
- [[#6246](https://github.com/apache/incubator-seata/pull/6246)] 在maven打包的同时打包前端资源
- [[#6268](https://github.com/apache/incubator-seata/pull/6268)] 更新console模块 npmjs 过时依赖
- [[#6271](https://github.com/apache/incubator-seata/pull/6271)] 统一git信息
- [[#6265](https://github.com/apache/incubator-seata/pull/6265)] 优化在 arm64 上构建前端失败的问题
- [[#6267](https://github.com/apache/incubator-seata/pull/6267)] 增加 Server 反序列化校验
- [[#6275](https://github.com/apache/incubator-seata/pull/6275)] 优化.asf.yaml文件中的label格式
- [[#6291](https://github.com/apache/incubator-seata/pull/6291)] 优化seata-server在idea等开发工具运行时，控制台未输出完整日志的问题
- [[#6283](https://github.com/apache/incubator-seata/pull/6283)] 增加兼容模块支持 io.seata APIs
- [[#6294](https://github.com/apache/incubator-seata/pull/6294)] 拆分前端资源打包流程到单独的profile
- [[#6285](https://github.com/apache/incubator-seata/pull/6285)] 优化控台中时间查询条件不准确的问题
- [[#6297](https://github.com/apache/incubator-seata/pull/6297)] 修复 `maven-pmd-plugin` 相关的问题
- [[#6298](https://github.com/apache/incubator-seata/pull/6298)] 重命名包名为 org.apache.seata
- [[#6302](https://github.com/apache/incubator-seata/pull/6302)] 增加 io.seata shade 打包方案
- [[#6306](https://github.com/apache/incubator-seata/pull/6306)] 替换一些URL 至 org/apache/seata
- [[#6304](https://github.com/apache/incubator-seata/pull/6304)] 禁用 OSSRH 发布工作流
- [[#6310](https://github.com/apache/incubator-seata/pull/6310)] seata-server兼容io.seata包
- [[#6301](https://github.com/apache/incubator-seata/pull/6301)] 升级console前端依赖及支持的nodejs版本
- [[#6301](https://github.com/apache/incubator-seata/pull/6312)] 添加saga相关的io.seata兼容性API
- [[#6313](https://github.com/apache/incubator-seata/pull/6313)] console展示版本号
- [[#6315](https://github.com/apache/incubator-seata/pull/6315)] 兼容低版本的SPI
- [[#6327](https://github.com/apache/incubator-seata/pull/6327)] 兼容 integration.http 和 integration.http.Jakarta API
- [[#6328](https://github.com/apache/incubator-seata/pull/6328)] 兼容 integration.grpc API
- [[#6330](https://github.com/apache/incubator-seata/pull/6330)] 去除 mariadb API
- [[#6329](https://github.com/apache/incubator-seata/pull/6312)] 添加 saga 子组件的 io.seata 兼容性 API
- [[#6254](https://github.com/apache/incubator-seata/pull/6254)] 优化Hessian 序列化
- [[#6343](https://github.com/apache/incubator-seata/pull/6343)] 兼容tm 模块和rm-datasource模块
- [[#6345](https://github.com/apache/incubator-seata/pull/6345)] 兼容tcc模块
- [[#6332](https://github.com/apache/incubator-seata/pull/6332)] 分发包中移除 mysql 依赖
- [[#6343](https://github.com/apache/incubator-seata/pull/6343)] 兼容 TM 模块和 rm-datasource 模块
- [[#6349](https://github.com/apache/incubator-seata/pull/6349)] 迁移  dockerhub 仓库
- [[#6357](https://github.com/apache/incubator-seata/pull/6357)] 优化协议编解码的序列化/反序列化
- [[#6356](https://github.com/apache/incubator-seata/pull/6356)] 去除健康检查页面的鉴权
- [[#6360](https://github.com/apache/incubator-seata/pull/6360)] 优化部分链接 401 的问题
- [[#6350](https://github.com/apache/incubator-seata/pull/6350)] 移除 enableDegrade 配置
- [[#6366](https://github.com/apache/incubator-seata/pull/6366)] 优化globaltransaction向下兼容性
- [[#6369](https://github.com/apache/incubator-seata/pull/6369)] 优化 arm64 ci
- [[#6386](https://github.com/apache/incubator-seata/pull/6386)] 在 `ConfigurationCache` 类中，将 `byte-buddy` 替换为JDK代理
- [[#6391](https://github.com/apache/incubator-seata/pull/6091)] 禁止重复注册TCC资源
- [[#6393](https://github.com/apache/incubator-seata/pull/6393)] 元数据同步前判断版本,并增加重试功能
- [[#6387](https://github.com/apache/incubator-seata/pull/6387)] 优化tcc使用兼容
- [[#6403](https://github.com/apache/incubator-seata/pull/6403)] 优化 Config 兼容模块
- [[#6402](https://github.com/apache/incubator-seata/pull/6402)] 优化rm-datasource向下兼容
- [[#6419](https://github.com/apache/incubator-seata/pull/6419)] 优化integration-tx-api向下兼容
- [[#6427](https://github.com/apache/incubator-seata/pull/6427)] 支持spi、saga、spring模块的向下兼容
- [[#6442](https://github.com/apache/incubator-seata/pull/6442)] 阐明 if
- [[#6487](https://github.com/apache/incubator-seata/pull/6487)] 修复错误包名以及单词
- [[#6458](https://github.com/apache/incubator-seata/pull/6458)] 增加MAC地址null值检查
- [[#6516](https://github.com/apache/incubator-seata/pull/6516)] 优化代码格式
- [[#6429](https://github.com/apache/incubator-seata/pull/6429)] 移除重复注释
- [[#6405](https://github.com/apache/incubator-seata/pull/6405)] 修复 kotlin 编译失败
- [[#6412](https://github.com/apache/incubator-seata/pull/6412)] 优化 core 兼容模块
- [[#6518](https://github.com/apache/incubator-seata/pull/6518)] 优化 ConfigurationCache 代理方法
- [[#6529](https://github.com/apache/incubator-seata/pull/6529)] 优化发布插件
- [[#6548](https://github.com/apache/incubator-seata/pull/6548)] 升级byte-buddy版本至1.14.15
- [[#6539](https://github.com/apache/incubator-seata/pull/6539)] 增加组件 license
- [[#6540](https://github.com/apache/incubator-seata/pull/6540)] 排除  com.google.guava:listenablefuture 依赖
- [[#6549](https://github.com/apache/incubator-seata/pull/6549)] 支持macos arm架构单测
- [[#6558](https://github.com/apache/incubator-seata/pull/6558)] 移除 mysql-connector-java 依赖
- [[#6570](https://github.com/apache/incubator-seata/pull/6570)] 添加 notice 文件
- [[#6578](https://github.com/apache/incubator-seata/pull/6578)] registry.conf 补充raft配置
- [[#6576](https://github.com/apache/incubator-seata/pull/6576)] 移除 oracle 数据类型序列化扩展
- [[#6583](https://github.com/apache/incubator-seata/pull/6583)] 优化默认编译不依赖 Git 环境
- [[#6585](https://github.com/apache/incubator-seata/pull/6585)] 优化 compatible 模块的配置
- [[#6597](https://github.com/apache/incubator-seata/pull/6597)] 从源码中移除 binary 包
- [[#6605](https://github.com/apache/incubator-seata/pull/6605)] 订正  license 和 notice
- [[#6609](https://github.com/apache/incubator-seata/pull/6609)] 订正  notice 文件
- [[#6610](https://github.com/apache/incubator-seata/pull/6610)] 订正  notice 文件

### security:
- [[#6069](https://github.com/apache/incubator-seata/pull/6069)] 升级Guava依赖版本，修复安全漏洞
- [[#6144](https://github.com/apache/incubator-seata/pull/6144)] 升级Nacos依赖版本至1.4.6
- [[#6145](https://github.com/apache/incubator-seata/pull/6145)] 升级 jettison依赖版本至1.5.4
- [[#6147](https://github.com/apache/incubator-seata/pull/6147)] 升级 kafka-clients依赖至3.6.1
- [[#6339](https://github.com/apache/incubator-seata/pull/6339)] 升级 spring mvc 和 tomcat.embed 依赖
- [[#6340](https://github.com/apache/incubator-seata/pull/6340)] 升级和整理依赖
- [[#6362](https://github.com/apache/incubator-seata/pull/6362)] 升级 Spring 相关的依赖
- [[#6375](https://github.com/apache/incubator-seata/pull/6375)] 覆盖 console 前端安全漏洞

### test:
- [[#6081](https://github.com/apache/incubator-seata/pull/6081)] 添加 `test-os.yml` 用于测试seata在各种操作系统下的运行情况
- [[#6125](https://github.com/apache/incubator-seata/pull/6125)] TransactionTemplateTest单测unbind xid
- [[#6157](https://github.com/apache/incubator-seata/pull/6157)] 增加common模块单测覆盖率
- [[#6250](https://github.com/apache/incubator-seata/pull/6250)] 增加seata-core模块单测覆盖率
- [[#6325](https://github.com/apache/incubator-seata/pull/6325)] 修复mock-server相关测试用例
- [[#6430](https://github.com/apache/incubator-seata/pull/6430)] 增加 common 模块单元测试覆盖率
- [[#6456](https://github.com/apache/incubator-seata/pull/6456)] 调整动态配置监听测试用例
- [[#6466](https://github.com/apache/incubator-seata/pull/6466)] 支持redis的集成测试
- [[#6484](https://github.com/apache/incubator-seata/pull/6484)] 修复FileConfigurationTest和MockServerTest失败
- [[#6545](https://github.com/apache/incubator-seata/pull/6545)] 修复 TestConfigCustomSPI 兼容性测试失败
- [[#6560](https://github.com/apache/incubator-seata/pull/6560)] 修复 mockserver test，不在 Runtime.getRuntime().addShutdownHook 中关闭
- [[#6565](https://github.com/apache/incubator-seata/pull/6565)] 修复 testCompensationStateMachine 与mockServer单测冲突

### refactor:
- [[#6280](https://github.com/apache/incubator-seata/pull/6280)] 使用diagram-js重构Saga设计器
- [[#6269](https://github.com/apache/incubator-seata/pull/6269)] 统一Seata异常规范
- [[#6420](https://github.com/apache/incubator-seata/pull/6420)] 优化配置缓存

非常感谢以下 contributors 的代码贡献。若有无意遗漏，请报告。

<!-- 请确保您的 GitHub ID 在以下列表中 -->
- [slievrly](https://github.com/slievrly)
- [ptyin](https://github.com/ptyin)
- [laywin](https://github.com/laywin)
- [imcmai](https://github.com/imcmai)
- [DroidEye2ONGU](https://github.com/DroidEye2ONGU)
- [funky-eyes](https://github.com/funky-eyes)
- [Bughue](https://github.com/Bughue)
- [wangliang181230](https://github.com/wangliang181230)
- [ggbocoder](https://github.com/ggbocoder)
- [leezongjie](https://github.com/leezongjie)
- [l81893521](https://github.com/l81893521)
- [baiyangtx](https://github.com/baiyangtx)
- [lightClouds917](https://github.com/lightClouds917)
- [xingfudeshi](https://github.com/xingfudeshi)
- [PleaseGiveMeTheCoke](https://github.com/PleaseGiveMeTheCoke)
- [sunrui1225](https://github.com/sunrui1225)
- [PeppaO](https://github.com/PeppaO)
- [AlbumenJ](https://github.com/AlbumenJ)
- [dreamskyvision](https://github.com/dreamskyvision)
- [jsbxyyx](https://github.com/jsbxyyx)
- [liuqiufeng](https://github.com/liuqiufeng)
- [saberyjs](https://github.com/SABERYJS)
- [gggyd123](https://github.com/gggyd123)
- [jonasHanhan](https://github.com/jonasHanhan)
- [Code-breaker1998](https://github.com/Code-breaker1998)
- [yixia](https://github.com/wt-better)
- [MikhailNavitski](https://github.com/MikhailNavitski)
- [deung](https://github.com/deung)
- [tanyaofei](https://github.com/tanyaofei)
- [xjlgod](https://github.com/xjlgod)
- [TakeActionNow2019](https://github.com/TakeActionNow2019)
- [sunxunle](https://github.com/sunxunle)
- [bageyang](https://github.com/bageyang)
- [YeonCheolGit](https://github.com/YeonCheolGit)

同时，我们收到了社区反馈的很多有价值的issue和建议，非常感谢大家。
