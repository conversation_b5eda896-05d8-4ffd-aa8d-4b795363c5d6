<!--
    Licensed to the Apache Software Foundation (ASF) under one or more
    contributor license agreements.  See the NOTICE file distributed with
    this work for additional information regarding copyright ownership.
    The ASF licenses this file to You under the Apache License, Version 2.0
    (the "License"); you may not use this file except in compliance with
    the License.  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0
    
    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
-->
### 2.4.0

<details>
  <summary><mark>Release notes</mark></summary>

### Apache Seata(incubating) 2.4.0

Apache Seata(incubating) 2.4.0 发布。

Apache Seata(incubating) 是一款开源的分布式事务解决方案，提供高性能和简单易用的分布式事务服务。

此版本更新如下：

### feature:

- [[#7157](https://github.com/apache/incubator-seata/pull/7157)] 将console迁移至namingserver中
- [[#7213](https://github.com/apache/incubator-seata/pull/7213)] 支持 kingbase xa 模式

### bugfix:

- [[#7104](https://github.com/apache/incubator-seata/pull/7104)] 修复SeataApplicationListener在低版本springboot未实现supportsSourceType方法的问题
- [[#7116](https://github.com/apache/incubator-seata/pull/7116)] 修复 seata.server.raft.ssl 前缀不存在的问题
- [[#7112](https://github.com/apache/incubator-seata/pull/7112)] 校验是否IPv6网络ip取消必须以fe80开始的条件
- [[#7107](https://github.com/apache/incubator-seata/pull/7107)] 修复tcc模式下，当业务对象为代理对象时，解析注解失败问题。
- [[#7124](https://github.com/apache/incubator-seata/pull/7124)] GlobalTransactionScanner.afterPropertiesSet方法需要做扫描检查
- [[#7135](https://github.com/apache/incubator-seata/pull/7135)] 回滚时遇到唯一索引冲突视为脏写
- [[#7150](https://github.com/apache/incubator-seata/pull/7150)] raft节点之前时间差，follower节点无法同步数据
- [[#7102](https://github.com/apache/incubator-seata/pull/7150)] 将XA模式预提交事务从提交阶段修改为关闭前阶段
- [[#7188](https://github.com/apache/incubator-seata/pull/7188)] 修复 BusinessActionContext 中缺少的 branchType
- [[#7219](https://github.com/apache/incubator-seata/pull/7219)] 修复 NotSupportExc 有些情况下不能被正确抛出
- [[#7241](https://github.com/apache/incubator-seata/pull/7241)] 升级 tomcat-embed-core 至 9.0.99 版本以解决 CVE-2025-24813 
- [[#7272](https://github.com/apache/incubator-seata/pull/7272)] 修复全局事务显示问题
- [[#7277](https://github.com/apache/incubator-seata/pull/7277)] 修复MySQL jdbc驱动无法正常找到的问题

### optimize:

- [[#6828](https://github.com/apache/incubator-seata/pull/6828)] seata-spring-boot-starter兼容file.conf和registry.conf
- [[#7012](https://github.com/apache/incubator-seata/pull/7012)] 当主键超过1000个时，使用union拼接sql，可以使用索引
- [[#7075](https://github.com/apache/incubator-seata/pull/7075)] 当channel为空时，快速失败，以便于减少不必要的等待
- [[#7089](https://github.com/apache/incubator-seata/pull/7089)] 新增instance注册到注册中心的接口
- [[#7093](https://github.com/apache/incubator-seata/pull/7093)] 增加jdk21的工作流测试
- [[#7088](https://github.com/apache/incubator-seata/pull/7088)] 将日志中英文缩写改为全拼
- [[#7064](https://github.com/apache/incubator-seata/pull/7064)] 移除不必要的空校验
- [[#7130](https://github.com/apache/incubator-seata/pull/7130)] 暴漏一些关于Druid, Hikari, 和DBCP的保活配置项
- [[#7131](https://github.com/apache/incubator-seata/pull/7131)] 移除 org.codehaus.jackson 依赖
- [[#7134](https://github.com/apache/incubator-seata/pull/7134)] 升级 tomcat-embed 至 9.0.98 版本
- [[#7138](https://github.com/apache/incubator-seata/pull/7138)] 移除 org.eclipse.jetty 依赖
- [[#7139](https://github.com/apache/incubator-seata/pull/7139)] 升级 xstream 至 1.4.21 版本
- [[#7141](https://github.com/apache/incubator-seata/pull/7141)] 去除未使用的依赖
- [[#7142](https://github.com/apache/incubator-seata/pull/7142)] 升级 commons-compress 至 1.27.1 版本
- [[#7149](https://github.com/apache/incubator-seata/pull/7149)] 修复./distribution/NOTICE.md文件中的异常字符串显示问题
- [[#7170](https://github.com/apache/incubator-seata/pull/7170)] 通过调整线程数优化 Seata 客户端 I/O 处理
- [[#7187](https://github.com/apache/incubator-seata/pull/7187)] 增加dependency-check-maven 插件来检测潜在的漏洞
- [[#7179](https://github.com/apache/incubator-seata/pull/7179)] 使用共享的 EventLoop 来减少 TM 和 RM 客户端的线程开销并提高性能
- [[#7194](https://github.com/apache/incubator-seata/pull/7194)] 自动跳过对AbstractRoutingDataSource类型数据源的代理
- [[#7215](https://github.com/apache/incubator-seata/pull/7215)] 拦截控制台写操作的非leader的raft请求
- [[#7224](https://github.com/apache/incubator-seata/pull/7224)] 优化控制台的changeGlobalStatus接口·
- [[#7222](https://github.com/apache/incubator-seata/pull/7222)] raft模式下控制台接口响应全局锁信息时增加vgroup字段
- [[#7229](https://github.com/apache/incubator-seata/pull/7229)] 更新 Notice
- [[#7234](https://github.com/apache/incubator-seata/pull/7234)] 优化raft对接namingserve时的服务发现逻辑
- [[#7242](https://github.com/apache/incubator-seata/pull/7242)] 更改参考案例下的ratelimit配置
- [[#7259](https://github.com/apache/incubator-seata/pull/7259)] 将logback appender配置转移到yml配置
- [[#6998](https://github.com/apache/incubator-seata/pull/6998)] 跳过协议版本v0不支持的request
- [[#7250](https://github.com/apache/incubator-seata/pull/7250)] 适配 client_protocol_version > server_protocol_version场景
- [[#7232](https://github.com/apache/incubator-seata/pull/7232)] 增加 license header
- [[#7260](https://github.com/apache/incubator-seata/pull/7260)] 升级 npmjs 依赖版本
- [[#7284](https://github.com/apache/incubator-seata/pull/7284)] 增加 dependency-check profile
- [[#6756](https://github.com/apache/incubator-seata/pull/6756)] seata服务单点限流支持
- [[#7073](https://github.com/apache/incubator-seata/pull/7073)] 支持虚拟线程，用ReentrantLock替换synchronized的用法
- [[#7037](https://github.com/apache/incubator-seata/pull/7037)] 支持UndoLog的fury序列化方式
- [[#7069](https://github.com/apache/incubator-seata/pull/7069)] Raft集群模式支持地址转换
- [[#7038](https://github.com/apache/incubator-seata/pull/7038)] 支持Fury序列化器
- [[#7114](https://github.com/apache/incubator-seata/pull/7114)] 支持raft集群注册至namingserver
- [[#7133](https://github.com/apache/incubator-seata/pull/7133)] 实现对残留的end状态事务定时处理
- [[#7171](https://github.com/apache/incubator-seata/pull/7171)] 客户端支持 EpollEventLoopGroup
- [[#7183](https://github.com/apache/incubator-seata/pull/7183)] 客户端支持通过namingserver发现raft节点
- [[#7182](https://github.com/apache/incubator-seata/pull/7182)] 采用peerId的ip作为raft节点的host
- [[#7181](https://github.com/apache/incubator-seata/pull/7181)] raft实现域名解析并选择peerId
- [[#7223](https://github.com/apache/incubator-seata/pull/7223)] 使用 Palantir java 格式应用 Spotless
- [[#7283](https://github.com/apache/incubator-seata/pull/7283)] 使用重试逻辑优化事务的结束


### security:
- [[#6069](https://github.com/apache/incubator-seata/pull/6069)] 升级Guava依赖版本，修复安全漏洞
- [[#6144](https://github.com/apache/incubator-seata/pull/6144)] 升级Nacos依赖版本至1.4.6
- [[#6145](https://github.com/apache/incubator-seata/pull/6145)] 升级 jettison依赖版本至1.5.4
- [[#6147](https://github.com/apache/incubator-seata/pull/6147)] 升级 kafka-clients依赖至3.6.1
- [[#6338](https://github.com/apache/incubator-seata/pull/6338)] 升级 jackson 依赖版本
- [[#7201](https://github.com/apache/incubator-seata/pull/7202)] 升级 protobuf 版本到 3.25.5
- [[#7214](https://github.com/apache/incubator-seata/pull/7214)] 升级 jackson 至 2.18.3 版本
- [[#7249](https://github.com/apache/incubator-seata/pull/7249)] 升级 axios 至 1.8.2 版本



### test:

- [[#7092](https://github.com/apache/incubator-seata/pull/7092)] 修复NacosMockTest测试方法并行导致测试结果被干扰失败的问题
- [[#7098](https://github.com/apache/incubator-seata/pull/7098)] 增加 `seata-common` 模块的测试用例
- [[#7160](https://github.com/apache/incubator-seata/pull/7160)] 在 LowerCaseLinkHashMapTest 中重构测试，以使用参数化单元测试
- [[#7167](https://github.com/apache/incubator-seata/pull/7167)] 重构了 DurationUtilTest 中的测试，以简化并使用参数化单元测试
- [[#7189](https://github.com/apache/incubator-seata/pull/7189)] 修复saga测试用例运行异常
- [[#7197](https://github.com/apache/incubator-seata/pull/7197)] 为 config 模块添加 UT 测试用例
- [[#7199](https://github.com/apache/incubator-seata/pull/7199)] 增加 client processor 单测用例
- [[#7203](https://github.com/apache/incubator-seata/pull/7203)] 重构了 rm.datasource.sql.Druid 和 seata-sqlparser-druid 模块中的测试
- [[#7221](https://github.com/apache/incubator-seata/pull/7221)] 增加 gRPC Encoder/Decoder的测试用例
- [[#7227](https://github.com/apache/incubator-seata/pull/7227)] 为 seata-discovery-consul 增加mock测试
- [[#7233][https://github.com/apache/incubator-seata/pull/7233]] 增加对 seata-discovery-etcd3 的mock测试
- [[#7243](https://github.com/apache/incubator-seata/pull/7243)] 增加对 seata-discovery-eureka的单测
- [[#7255](https://github.com/apache/incubator-seata/pull/7255)] 补充更多seata-discovery-eureka模块的单测提高覆盖率
### refactor:

- [[#7145](https://github.com/apache/incubator-seata/pull/7145)] 重构不满足 license 要求的代码
- [[#7236](https://github.com/apache/incubator-seata/pull/7236)] 将 org.apache.seata.server.storage.raft.sore 中的文件夹名称从 sore 更改为 store

非常感谢以下 contributors 的代码贡献。若有无意遗漏，请报告。

<!-- 请确保您的 GitHub ID 在以下列表中 -->

- [slievrly](https://github.com/slievrly)
- [lyl2008dsg](https://github.com/lyl2008dsg)
- [remind](https://github.com/remind)
- [xjlgod](https://github.com/xjlgod)
- [lightClouds917](https://github.com/lightClouds917)
- [GoodBoyCoder](https://github.com/GoodBoyCoder)
- [PeppaO](https://github.com/PeppaO)
- [funky-eyes](https://github.com/funky-eyes)
- [MaoMaoandSnail](https://github.com/MaoMaoandSnail)
- [psxjoy](https://github.com/psxjoy)
- [xiaoxiangyeyu0](https://github.com/xiaoxiangyeyu0)
- [wxrqforever](https://github.com/wxrqforever)
- [xingfudeshi](https://github.com/xingfudeshi)
- [YongGoose](https://github.com/YongGoose)
- [Monilnarang](https://github.com/Monilnarang)
- [iAmClever](https://github.com/iAmClever)
- [s-ramyalakshmi](https://github.com/s-ramyalakshmi)
- [YoWuwuuuw](https://github.com/YoWuwuuuw)
- [AndrewSf](https://github.com/andrewseif)
- [bigcyy](https://github.com/bigcyy)
- [wjwang00](https://github.com/wjwang00)

同时，我们收到了社区反馈的很多有价值的issue和建议，非常感谢大家。

</details>
