<!--
    Licensed to the Apache Software Foundation (ASF) under one or more
    contributor license agreements.  See the NOTICE file distributed with
    this work for additional information regarding copyright ownership.
    The ASF licenses this file to You under the Apache License, Version 2.0
    (the "License"); you may not use this file except in compliance with
    the License.  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0
    
    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
-->
### 1.6.1

[source](https://github.com/seata/seata/archive/v1.6.1.zip) |
[binary](https://github.com/seata/seata/releases/download/v1.6.1/seata-server-1.6.1.zip)

<details>
  <summary><mark>Release notes</mark></summary>


### Seata 1.6.1

Seata 1.6.1  发布。

Seata 是一款开源的分布式事务解决方案，提供高性能和简单易用的分布式事务服务。

此版本更新如下：

### feature:
- [[#5115](https://github.com/seata/seata/pull/5115)] 支持 `spring-boot:3.x`

### bugfix:
- [[#5179](https://github.com/seata/seata/pull/5179)] 修复使用Eureka作为注册中心ClassNotFoundException问题

### optimize：
- [[#5120](https://github.com/seata/seata/pull/5120)] 统一yml文件中的配置项格式
- [[#5180](https://github.com/seata/seata/pull/5180)] GlobalTransactionScanner,SeataAutoDataSourceProxyCreator 创建bean用static修饰
- [[#5182](https://github.com/seata/seata/pull/5182)] 修复 Saga 可视化设计器 GGEditor 安全漏洞
- [[#5183](https://github.com/seata/seata/pull/5183)] 优化配置开关的默认值

非常感谢以下 contributors 的代码贡献。若有无意遗漏，请报告。

<!-- 请确保您的 GitHub ID 在以下列表中 -->
- [slievrly](https://github.com/slievrly)
- [wangliang181230](https://github.com/wangliang181230)
- [xingfudeshi](https://github.com/xingfudeshi)
- [whxxxxx](https://github.com/whxxxxx)
- [xssdpgy](https://github.com/xssdpgy)

同时，我们收到了社区反馈的很多有价值的issue和建议，非常感谢大家。


#### Link

- **Seata:** https://github.com/seata/seata
- **Seata-Samples:** https://github.com/seata/seata-samples
- **Release:** https://github.com/seata/seata/releases
- **WebSite:** https://seata.io

</details>
