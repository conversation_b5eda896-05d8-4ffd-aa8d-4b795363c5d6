<?xml version="1.0" encoding="UTF-8"?>
<!--

    Licensed to the Apache Software Foundation (ASF) under one or more
    contributor license agreements.  See the NOTICE file distributed with
    this work for additional information regarding copyright ownership.
    The ASF licenses this file to You under the Apache License, Version 2.0
    (the "License"); you may not use this file except in compliance with
    the License.  You may obtain a copy of the License at

        http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.

-->
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>org.apache</groupId>
        <artifactId>apache</artifactId>
        <version>19</version>
        <relativePath/>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <groupId>org.apache.seata</groupId>
    <artifactId>seata-build</artifactId>
    <packaging>pom</packaging>
    <version>${revision}</version>

    <name>Seata Build ${project.version}</name>
    <description>plugin management for Seata built with Maven</description>
    <licenses>
        <license>
            <name>Apache License, Version 2.0</name>
            <url>https://www.apache.org/licenses/LICENSE-2.0</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <organization>
        <name>Apache</name>
        <url>https://github.com/apache</url>
    </organization>

    <url>https://seata.apache.org</url>

    <developers>
        <developer>
            <id>Seata</id>
            <name>Seata</name>
            <url>https://seata.apache.org</url>
            <email><EMAIL></email>
        </developer>
    </developers>

    <issueManagement>
        <system>github</system>
        <url>https://github.com/apache/incubator-seata/issues</url>
    </issueManagement>

    <scm>
        <url>**************:apache/incubator-seata.git</url>
        <connection>scm:**************:apache/incubator-seata.git</connection>
        <developerConnection>scm:**************:apache/incubator-seata.git</developerConnection>
    </scm>

    <properties>
        <!-- seata version -->
        <revision>2.5.0-SNAPSHOT</revision>

        <!-- Compiler settings properties -->
        <java.version>1.8</java.version>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

        <!-- The version of spring-boot for 'spring-boot-dependencies' and 'spring-boot-maven-plugin' -->
        <spring-boot.version>2.7.18</spring-boot.version>
        <spring-framework.version>5.3.39</spring-framework.version>

        <!--  server side dependency-->
        <kafka-appender.version>0.2.0-RC2</kafka-appender.version>
        <kafka-clients.version>3.6.1</kafka-clients.version>
        <snakeyaml.version>2.0</snakeyaml.version>
        <bucket4j.version>8.1.0</bucket4j.version>

        <!-- For test -->
        <junit-jupiter.version>5.8.2</junit-jupiter.version>
        <junit-platform.version>1.8.2</junit-platform.version>

        <!-- Maven plugin versions -->
        <!-- Build -->
        <easyj-maven-plugin.version>1.1.5</easyj-maven-plugin.version>
        <maven-clean-plugin.version>3.1.0</maven-clean-plugin.version>
        <!-- Compiler -->
        <maven-compiler-plugin.version>3.8.1</maven-compiler-plugin.version>
        <protobuf-maven-plugin.version>0.6.1</protobuf-maven-plugin.version>
        <kotlin-maven-plugin.version>1.9.21</kotlin-maven-plugin.version>
        <!-- Check -->
        <maven-pmd-plugin.version>3.8</maven-pmd-plugin.version>
        <p3c-pmd.version>1.3.6</p3c-pmd.version>
        <maven-javadoc-plugin.version>3.0.0</maven-javadoc-plugin.version>
        <license-maven-plugin.version>4.0</license-maven-plugin.version>
        <mojo-license-maven-plugin.version>1.20</mojo-license-maven-plugin.version>
        <maven-checkstyle-plugin.version>3.1.1</maven-checkstyle-plugin.version>
        <spotless-maven-plugin.version>2.44.3</spotless-maven-plugin.version>
        <palantirJavaFormat.version>2.38.0</palantirJavaFormat.version>
        <maven-enforcer-plugin.version>3.0.0-M3</maven-enforcer-plugin.version>
        <dependency-check-maven.version>12.1.0</dependency-check-maven.version>
        <!-- Test -->
        <maven-surefire-plugin.version>3.0.0-M5</maven-surefire-plugin.version>
        <jacoco-maven-plugin.version>0.8.7</jacoco-maven-plugin.version>
        <!-- Packaging -->
        <maven-source-plugin.version>2.2.1</maven-source-plugin.version>
        <maven-resources-plugin.version>3.2.0</maven-resources-plugin.version>
        <maven-jar-plugin.version>3.2.2</maven-jar-plugin.version>
        <maven-shade-plugin.version>2.4.3</maven-shade-plugin.version>
        <maven-dependency-plugin.version>3.0.2</maven-dependency-plugin.version>
        <maven-assembly-plugin.version>3.0.0</maven-assembly-plugin.version>
        <jib-maven-plugin.version>3.3.0</jib-maven-plugin.version>
        <git-commit-id-plugin.version>4.9.10</git-commit-id-plugin.version>
        <frontend-maven-plugin.version>1.15.0</frontend-maven-plugin.version>
        <!-- Deploy && GPG -->
        <maven-deploy-plugin.version>2.8.2</maven-deploy-plugin.version>
        <nexus-staging-maven-plugin.version>1.6.7</nexus-staging-maven-plugin.version>
        <maven-gpg-plugin.version>1.6</maven-gpg-plugin.version>
        <!-- Other -->
        <maven-antrun-plugin.version>1.8</maven-antrun-plugin.version>
        <os-maven-plugin.version>1.5.0.Final</os-maven-plugin.version>

        <!-- Default values of the Maven plugins -->
        <checkstyle.skip>true</checkstyle.skip>
        <license.skip>true</license.skip>
        <pmd.skip>true</pmd.skip>
        <maven.test.skip>false</maven.test.skip>
        <maven.git-commit-id.skip>true</maven.git-commit-id.skip>

        <maven.surefire.argLine></maven.surefire.argLine>
        <maven.surefire.excludes></maven.surefire.excludes>

        <!-- For docker image-->
        <image.publish.skip>true</image.publish.skip>
        <image.name>${IMAGE_NAME}</image.name>
        <image.tags>latest</image.tags>

        <dependencies.copy.skip>true</dependencies.copy.skip>
        <mysql.copy.skip>true</mysql.copy.skip>
    </properties>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <version>${maven-deploy-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-shade-plugin</artifactId>
                    <version>${maven-shade-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-dependency-plugin</artifactId>
                    <version>${maven-dependency-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-checkstyle-plugin</artifactId>
                    <version>${maven-checkstyle-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>com.diffplug.spotless</groupId>
                    <artifactId>spotless-maven-plugin</artifactId>
                    <version>${spotless-maven-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <version>${maven-javadoc-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.xolstice.maven.plugins</groupId>
                    <artifactId>protobuf-maven-plugin</artifactId>
                    <version>${protobuf-maven-plugin.version}</version>
                    <configuration>
                        <!-- Solve the problem that protobuf compilation fails due to too long command line under the window operating system,
                        ref: https://www.xolstice.org/protobuf-maven-plugin/usage.html -->
                        <useArgumentFile>true</useArgumentFile>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>com.mycila</groupId>
                    <artifactId>license-maven-plugin</artifactId>
                    <version>${license-maven-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>license-maven-plugin</artifactId>
                    <version>${mojo-license-maven-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>icu.easyj.maven.plugins</groupId>
                    <artifactId>easyj-maven-plugin</artifactId>
                    <version>${easyj-maven-plugin.version}</version>
                    <!-- This goal can replace flatten-maven-plugin to flatten the pom, and replace '${revision}' to the actual version. -->
                    <executions>
                        <execution>
                            <id>simplify-pom</id>
                            <goals>
                                <goal>simplify-pom</goal>
                            </goals>
                        </execution>
                    </executions>
                    <configuration>
                        <simplifiedPomFileName>.flattened-pom.xml</simplifiedPomFileName>
                        <useTabIndent>true</useTabIndent>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>pl.project13.maven</groupId>
                    <artifactId>git-commit-id-plugin</artifactId>
                    <version>${git-commit-id-plugin.version}</version>
                    <executions>
                        <execution>
                            <id>get-the-git-infos</id>
                            <goals>
                                <goal>revision</goal>
                            </goals>
                        </execution>
                    </executions>
                    <configuration>
                        <skip>${maven.git-commit-id.skip}</skip>
                        <verbose>true</verbose>
                        <dateFormat>yyyy-MM-dd'T'HH:mm:ssZ</dateFormat>
                        <generateGitPropertiesFile>true</generateGitPropertiesFile>
                        <generateGitPropertiesFilename>${project.build.outputDirectory}/seata-git.properties</generateGitPropertiesFilename>
                        <includeOnlyProperties>
                            <includeOnlyProperty>git.commit.message.full</includeOnlyProperty>
                            <includeOnlyProperty>git.remote.origin.url</includeOnlyProperty>
                            <includeOnlyProperty>git.branch</includeOnlyProperty>
                            <includeOnlyProperty>^git.build.(time|version)$</includeOnlyProperty>
                            <includeOnlyProperty>^git.commit.(id|time)$</includeOnlyProperty>
                            <includeOnlyProperty>git.dirty</includeOnlyProperty>
                        </includeOnlyProperties>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>com.github.eirslett</groupId>
                    <artifactId>frontend-maven-plugin</artifactId>
                    <version>${frontend-maven-plugin.version}</version>
                </plugin>
            </plugins>
        </pluginManagement>

        <plugins>
            <!-- Compiler -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.version}</version>
                <configuration>
                    <source>${maven.compiler.source}</source>
                    <target>${maven.compiler.target}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                    <parameters>true</parameters>
                </configuration>
            </plugin>
            <!-- Resources -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>${maven-resources-plugin.version}</version>
                <configuration>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
            <!-- Jar -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>${maven-jar-plugin.version}</version>
                <configuration>
                    <archive>
                        <addMavenDescriptor>true</addMavenDescriptor>
                        <index>true</index>
                        <manifest>
                            <addDefaultSpecificationEntries>true</addDefaultSpecificationEntries>
                            <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
                        </manifest>
                        <manifestEntries>
                            <Implementation-Build>${maven.build.timestamp}</Implementation-Build>
                        </manifestEntries>
                    </archive>
                    <excludes>
                        <exclude>**/META-INF/additional-spring-configuration-metadata.json</exclude>
                        <exclude>protobuf/**</exclude>
                        <exclude>**/*.proto</exclude>
                        <exclude>static/console-fe/**</exclude>
                    </excludes>
                </configuration>
            </plugin>
            <!-- Clean -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-clean-plugin</artifactId>
                <version>${maven-clean-plugin.version}</version>
                <configuration>
                    <filesets>
                        <fileset>
                            <directory>./</directory>
                            <includes>
                                <include>*-pom.xml</include>
                                <include>**/db_store/**</include>
                                <include>**/sessionStore/**</include>
                                <include>**/root.data</include>
                            </includes>
                            <followSymlinks>false</followSymlinks>
                        </fileset>
                    </filesets>
                </configuration>
            </plugin>
            <!-- EasyJ -->
            <plugin>
                <groupId>icu.easyj.maven.plugins</groupId>
                <artifactId>easyj-maven-plugin</artifactId>
            </plugin>
            <!-- Enforcer -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <version>${maven-enforcer-plugin.version}</version>
                <executions>
                    <execution>
                        <id>enforce-maven</id>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <requireMavenVersion>
                                    <version>[3.6.0,)</version>
                                </requireMavenVersion>
                            </rules>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <!-- Git commit id -->
            <plugin>
                <groupId>pl.project13.maven</groupId>
                <artifactId>git-commit-id-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <!-- profile: release -->
        <profile>
            <id>release</id>
            <properties>
                <maven.git-commit-id.skip>false</maven.git-commit-id.skip>
            </properties>
            <build>
                <plugins>
                    <!-- Javadoc -->
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-javadoc-plugin</artifactId>
                        <configuration>
                            <charset>${project.build.sourceEncoding}</charset>
                            <encoding>${project.build.sourceEncoding}</encoding>
                            <failOnError>false</failOnError>
                        </configuration>
                        <executions>
                            <execution>
                                <phase>package</phase>
                                <goals>
                                    <goal>jar</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                    <!-- GPG -->
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-gpg-plugin</artifactId>
                        <version>${maven-gpg-plugin.version}</version>
                        <executions>
                            <execution>
                                <id>sign-artifacts</id>
                                <phase>verify</phase>
                                <goals>
                                    <goal>sign</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <!-- profile: release-by-github-actions -->
        <profile>
            <id>release-by-github-actions</id>
            <properties>
                <gpg.arg1>--pinentry-mode</gpg.arg1>
                <gpg.arg2>loopback</gpg.arg2>
                <maven.git-commit-id.skip>false</maven.git-commit-id.skip>
            </properties>
        </profile>

        <!-- profile: args-for-test-by-jdk17-and-above -->
        <profile>
            <id>args-for-test-by-jdk17-and-above</id>
            <activation>
                <jdk>[17,)</jdk>
            </activation>
            <properties>
                <maven.surefire.argLine>
                    --add-opens java.base/java.lang=ALL-UNNAMED
                    --add-opens java.base/java.net=ALL-UNNAMED
                    --add-opens java.base/java.math=ALL-UNNAMED
                    --add-opens java.base/java.text=ALL-UNNAMED
                    --add-opens java.base/java.util=ALL-UNNAMED
                    --add-opens java.base/java.util.regex=ALL-UNNAMED
                    --add-opens java.base/java.util.concurrent=ALL-UNNAMED

                    --add-opens java.sql/java.sql=ALL-UNNAMED
                    --add-opens java.sql.rowset/javax.sql.rowset.serial=ALL-UNNAMED

                    -Dnet.bytebuddy.experimental=true
                </maven.surefire.argLine>
            </properties>
        </profile>

        <!-- profile: args-for-client-test -->
        <profile>
            <id>args-for-client-test</id>
            <properties>
                <maven.surefire.excludes>org/apache/seata/server/**/*.java,org/apache/seata/console/**/*.java</maven.surefire.excludes>
            </properties>
        </profile>
    </profiles>
</project>
